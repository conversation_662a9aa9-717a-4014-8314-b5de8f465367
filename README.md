# Amazon爬虫系统

一个高效的Amazon商品信息爬虫系统，支持多线程、代理轮换、智能延迟等功能。

## 🚀 主要功能

### ✅ 数据提取功能
- **标准化URL格式**: 所有商品URL统一为 `https://www.amazon.com/dp/{ASIN}` 格式
- **Prime配送信息**: 智能提取具体配送时间 (ONE DAY, TWO DAY, SAME DAY等)
- **完整商品信息**: ASIN, 标题, 价格, 评分, 图片, 购买数量等
- **多选择器策略**: 适应Amazon页面结构变化

### ✅ 系统特性
- **多线程爬取**: 支持并发处理多个任务
- **代理轮换**: 自动轮换代理IP，避免封禁
- **智能延迟**: 动态调整请求间隔
- **错误处理**: 503/429错误自动重试和代理切换
- **数据库集成**: 自动保存到MySQL数据库

## 📁 项目结构

```
├── test_crawler.py          # 主要爬虫代码
├── crawler_config.py        # 配置文件
├── run_crawler.py          # 爬虫启动脚本
├── offline_test_mode.py    # 离线测试工具
├── user_agents.py          # 用户代理管理
├── proxy_manager.py        # 代理管理
├── dynamic_delay_manager.py # 智能延迟管理
├── doc/                    # 测试HTML文件
│   ├── list.html
│   └── list2.html
└── logs/                   # 日志文件
```

## 🔧 配置说明

### 数据库配置 (crawler_config.py)
```python
DB_CONFIG = {
    'host': 'your_host',
    'user': 'your_user', 
    'password': 'your_password',
    'database': 'your_database',
    'port': 3306,
    'charset': 'utf8mb4'
}
```

### 爬虫配置
```python
CRAWLER_CONFIG = {
    'max_workers': 20,          # 最大线程数
    'request_timeout': 30,      # 请求超时时间
    'retry_count': 3,           # 重试次数
    'use_proxy': True,          # 是否使用代理
    'rotate_user_agent': True   # 是否轮换用户代理
}
```

## 🚀 使用方法

### 1. 启动爬虫
```bash
python run_crawler.py --workers 10
```

### 2. 离线测试
```bash
python offline_test_mode.py
```

### 3. 单任务测试
```python
from test_crawler import AmazonCrawler
from crawler_config import get_config

crawler = AmazonCrawler(get_config())
# 处理单个任务...
```

## 📊 数据库表结构

### zz_amazon_list_tasks (任务表)
- `id`: 任务ID
- `url`: 抓取URL
- `source_category_id`: 类目ID
- `max_pages_to_crawl`: 最大页数
- `status`: 任务状态

### zz_amazon_page_tasks (商品表)
- `entry_asin`: 商品ASIN
- `url`: 标准化商品URL
- `list_page_title`: 商品标题
- `list_page_price`: 商品价格
- `list_page_prime_info`: Prime配送信息
- `amazon_category_id`: 类目ID关联
- `list_task_id`: 任务ID关联

## 📈 数据提取示例

### URL格式化
```
原始: /dp/B0DM655RKW/ref=sr_1_1?keywords=...
标准: https://www.amazon.com/dp/B0DM655RKW
```

### Prime信息提取
```
原始: "Get FREE One-Day Delivery with Prime"
提取: "ONE DAY"

原始: "FREE Two-Day Shipping"  
提取: "TWO DAY"
```

## 🔍 测试验证

### 离线测试结果
```
✅ 总商品数: 36个
✅ URL格式正确率: 100%
✅ Prime信息覆盖率: 100%
✅ 具体配送信息: 97.2%

Prime配送类型分布:
- ONE DAY: 86.1%
- TWO DAY: 8.3%  
- SAME DAY: 2.8%
- OTHER: 2.8%
```

## 📋 数据分析查询

### 按配送类型统计
```sql
SELECT 
    list_page_prime_info,
    COUNT(*) as product_count,
    AVG(list_page_price) as avg_price
FROM zz_amazon_page_tasks 
WHERE list_page_prime_info IS NOT NULL
GROUP BY list_page_prime_info
ORDER BY product_count DESC;
```

### URL格式验证
```sql
SELECT 
    COUNT(*) as total,
    COUNT(CASE WHEN url LIKE 'https://www.amazon.com/dp/%' THEN 1 END) as standard_format
FROM zz_amazon_page_tasks;
```

## ⚠️ 注意事项

1. **代理配置**: 建议配置高质量代理池
2. **请求频率**: 避免过于频繁的请求
3. **错误处理**: 监控503/429错误频率
4. **数据备份**: 定期备份抓取的数据

## 🛠️ 故障排除

### 常见问题

**503错误频繁**
- 检查代理质量
- 调整请求间隔
- 减少并发线程数

**数据提取不完整**
- 检查页面结构变化
- 更新选择器配置
- 运行离线测试验证

**数据库连接失败**
- 检查数据库配置
- 确认网络连接
- 验证表结构

## 📝 更新日志

### v2.0 (当前版本)
- ✅ URL格式标准化
- ✅ Prime配送信息智能提取
- ✅ 多选择器策略
- ✅ 数据库字段关联优化
- ✅ 离线测试工具

### v1.0
- 基础爬虫功能
- 多线程支持
- 代理轮换
- 错误处理

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 查看日志文件: `logs/amazon_crawler.log`
- 运行测试工具: `python offline_test_mode.py`
- 检查数据库状态: 查询相关表结构和数据

---

**Amazon爬虫系统 - 高效、稳定、智能的商品信息抓取解决方案**
