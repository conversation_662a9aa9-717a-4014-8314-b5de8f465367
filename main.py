from config import AMAZON_US_SEARCH_ALIAS_MAP

def get_search_alias_from_nodepath(node_path, alias_map):
    """
    从完整的类目路径中提取顶层类目，并查找其搜索别名。
    """
    if not node_path:
        print("警告：输入的node_path为空。")
        return None
    
    # 1. 提取顶层类目名称
    try:
        top_level_category = node_path.split('/')[0].strip()
    except IndexError:
        print(f"警告：无法从'{node_path}'中分割出顶层类目。")
        return None
    
    # 2. 从映射表中查找别名
    alias = alias_map.get(top_level_category)
    
    if not alias:
        print(f"信息：在映射表中未找到'{top_level_category}'的别名，将使用默认值'aps'。")
        return 'aps' # 提供一个安全的回退选项
        
    return alias

# --- 示例用法 ---
# 假设这是从你的数据库中读取的一行数据
db_row_node_path_1 = "Arts, Crafts & Sewing/Beading & Jewelry Making/Beading Supplies/Bead Looms"
db_row_node_path_2 = "Home & Kitchen/Storage & Organization/Drawer Organizers"
db_row_node_path_3 = "A completely new category/Sub category" # 一个未知的类目

# 测试第一个类目
search_alias_1 = get_search_alias_from_nodepath(db_row_node_path_1, AMAZON_US_SEARCH_ALIAS_MAP)
print(f"路径 '{db_row_node_path_1}' -> 别名: '{search_alias_1}' -> URL参数: i={search_alias_1}")

# 测试第二个类目
search_alias_2 = get_search_alias_from_nodepath(db_row_node_path_2, AMAZON_US_SEARCH_ALIAS_MAP)
print(f"路径 '{db_row_node_path_2}' -> 别名: '{search_alias_2}' -> URL参数: i={search_alias_2}")

# 测试未知类目
search_alias_3 = get_search_alias_from_nodepath(db_row_node_path_3, AMAZON_US_SEARCH_ALIAS_MAP)
print(f"路径 '{db_row_node_path_3}' -> 姓名: '{search_alias_3}' -> URL参数: i={search_alias_3}")