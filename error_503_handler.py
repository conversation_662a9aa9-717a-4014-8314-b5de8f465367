#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon 503错误处理器
专门处理503 Service Unavailable错误的工具类
"""

import time
import random
import logging
from datetime import datetime, timedelta
from collections import defaultdict, deque
from anti_503_config import ANTI_503_CONFIG, ERROR_RECOVERY, get_503_delay, should_stop_on_error

logger = logging.getLogger(__name__)

class Error503Handler:
    """503错误处理器"""
    
    def __init__(self):
        self.error_stats = defaultdict(int)  # 错误统计
        self.error_history = deque(maxlen=100)  # 错误历史
        self.last_503_time = None  # 最后一次503错误时间
        self.consecutive_503_count = 0  # 连续503错误计数
        self.recovery_mode = False  # 是否处于恢复模式
        self.recovery_start_time = None  # 恢复模式开始时间
        
        # 请求频率控制
        self.request_times = deque(maxlen=100)  # 请求时间记录
        self.last_request_time = None
        
        # 会话管理
        self.session_start_time = datetime.now()
        self.session_request_count = 0
        
    def record_error(self, error_type, url=None, page_number=None):
        """记录错误"""
        now = datetime.now()
        
        self.error_stats[error_type] += 1
        self.error_history.append({
            'type': error_type,
            'time': now,
            'url': url,
            'page': page_number
        })
        
        if error_type == '503':
            self.last_503_time = now
            self.consecutive_503_count += 1
            logger.warning(f"记录503错误，连续次数: {self.consecutive_503_count}")
        
        # 检查是否需要进入恢复模式
        self._check_recovery_mode()
    
    def record_success(self):
        """记录成功请求"""
        now = datetime.now()
        self.request_times.append(now)
        self.last_request_time = now
        self.session_request_count += 1
        
        # 重置连续错误计数
        if self.consecutive_503_count > 0:
            logger.info(f"成功请求，重置连续503错误计数 (之前: {self.consecutive_503_count})")
            self.consecutive_503_count = 0
        
        # 检查是否可以退出恢复模式
        self._check_exit_recovery_mode()
    
    def should_stop_crawling(self):
        """判断是否应该停止抓取"""
        # 连续503错误过多
        if self.consecutive_503_count >= ANTI_503_CONFIG['error_503_max_retries']:
            logger.error(f"连续503错误 {self.consecutive_503_count} 次，建议停止抓取")
            return True
        
        # 最近错误率过高
        recent_error_rate = self._get_recent_error_rate()
        if recent_error_rate > 0.5:  # 50%错误率
            logger.error(f"最近错误率过高: {recent_error_rate:.2%}")
            return True
        
        return False
    
    def get_wait_time(self, error_type='503', attempt_count=None):
        """获取等待时间"""
        if attempt_count is None:
            attempt_count = self.consecutive_503_count
        
        if error_type == '503':
            base_delay = get_503_delay(attempt_count)
            
            # 如果处于恢复模式，增加延迟
            if self.recovery_mode:
                base_delay *= 2
            
            # 添加随机因子
            jitter = random.uniform(0.8, 1.2)
            return base_delay * jitter
        
        elif error_type == '429':
            return ERROR_RECOVERY['429']['initial_delay'] * (2 ** (attempt_count - 1))
        
        elif error_type == 'captcha':
            return ERROR_RECOVERY['captcha']['initial_delay']
        
        else:
            return random.uniform(10, 30)
    
    def should_change_proxy(self):
        """判断是否应该更换代理"""
        # 连续503错误
        if self.consecutive_503_count >= 2:
            return True
        
        # 最近错误率高
        if self._get_recent_error_rate() > 0.3:
            return True
        
        return False
    
    def should_change_user_agent(self):
        """判断是否应该更换用户代理"""
        # 连续503错误
        if self.consecutive_503_count >= 3:
            return True
        
        # 处于恢复模式
        if self.recovery_mode:
            return True
        
        return False
    
    def should_reduce_concurrency(self):
        """判断是否应该降低并发数"""
        # 连续503错误
        if self.consecutive_503_count >= 2:
            return True
        
        # 最近503错误频繁
        recent_503_count = self._get_recent_503_count()
        if recent_503_count >= 3:
            return True
        
        return False
    
    def get_page_delay(self, page_number, base_delay_range):
        """获取页面延迟"""
        base_min, base_max = base_delay_range
        
        # 根据页数调整延迟
        if page_number > ANTI_503_CONFIG['high_page_threshold']:
            multiplier = min(page_number / ANTI_503_CONFIG['high_page_threshold'], 
                           ANTI_503_CONFIG['max_delay_multiplier'])
            base_min *= multiplier
            base_max *= multiplier
        
        # 根据错误情况调整延迟
        if self.consecutive_503_count > 0:
            error_multiplier = 1 + (self.consecutive_503_count * 0.5)
            base_min *= error_multiplier
            base_max *= error_multiplier
        
        # 恢复模式增加延迟
        if self.recovery_mode:
            base_min *= 2
            base_max *= 2
        
        return random.uniform(base_min, base_max)
    
    def can_make_request(self):
        """判断是否可以发送请求"""
        now = datetime.now()
        
        # 检查请求频率
        if self.last_request_time:
            time_since_last = (now - self.last_request_time).total_seconds()
            min_interval = 60 / ANTI_503_CONFIG['requests_per_minute']
            
            if time_since_last < min_interval:
                return False, min_interval - time_since_last
        
        # 检查会话限制
        session_duration = (now - self.session_start_time).total_seconds()
        if (session_duration > ANTI_503_CONFIG['session_lifetime'] or 
            self.session_request_count >= ANTI_503_CONFIG['session_request_limit']):
            return False, "需要重新开始会话"
        
        return True, 0
    
    def reset_session(self):
        """重置会话"""
        self.session_start_time = datetime.now()
        self.session_request_count = 0
        logger.info("重置会话")
    
    def get_stats(self):
        """获取统计信息"""
        now = datetime.now()
        
        # 计算最近错误率
        recent_error_rate = self._get_recent_error_rate()
        recent_503_count = self._get_recent_503_count()
        
        # 会话信息
        session_duration = (now - self.session_start_time).total_seconds()
        
        return {
            'total_errors': dict(self.error_stats),
            'consecutive_503_count': self.consecutive_503_count,
            'recent_error_rate': recent_error_rate,
            'recent_503_count': recent_503_count,
            'recovery_mode': self.recovery_mode,
            'session_duration': session_duration,
            'session_request_count': self.session_request_count,
            'last_503_time': self.last_503_time,
            'can_make_request': self.can_make_request()[0]
        }
    
    def _check_recovery_mode(self):
        """检查是否需要进入恢复模式"""
        if self.recovery_mode:
            return
        
        # 连续503错误触发恢复模式
        if self.consecutive_503_count >= 2:
            self.recovery_mode = True
            self.recovery_start_time = datetime.now()
            logger.warning("进入恢复模式")
            return
        
        # 最近错误率过高触发恢复模式
        if self._get_recent_error_rate() > 0.4:
            self.recovery_mode = True
            self.recovery_start_time = datetime.now()
            logger.warning("因错误率过高进入恢复模式")
    
    def _check_exit_recovery_mode(self):
        """检查是否可以退出恢复模式"""
        if not self.recovery_mode:
            return
        
        now = datetime.now()
        
        # 恢复模式持续时间
        recovery_duration = (now - self.recovery_start_time).total_seconds()
        
        # 条件：无连续503错误 且 恢复时间足够 且 最近错误率低
        if (self.consecutive_503_count == 0 and 
            recovery_duration > 300 and  # 至少5分钟
            self._get_recent_error_rate() < 0.2):
            
            self.recovery_mode = False
            self.recovery_start_time = None
            logger.info("退出恢复模式")
    
    def _get_recent_error_rate(self, window_minutes=10):
        """获取最近的错误率"""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=window_minutes)
        
        recent_errors = [e for e in self.error_history if e['time'] > cutoff_time]
        recent_requests = [t for t in self.request_times if t > cutoff_time]
        
        total_recent = len(recent_errors) + len(recent_requests)
        if total_recent == 0:
            return 0
        
        return len(recent_errors) / total_recent
    
    def _get_recent_503_count(self, window_minutes=30):
        """获取最近的503错误数量"""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=window_minutes)
        
        recent_503_errors = [e for e in self.error_history 
                           if e['time'] > cutoff_time and e['type'] == '503']
        
        return len(recent_503_errors)

# 全局503错误处理器实例
error_503_handler = Error503Handler()

def handle_503_error(url=None, page_number=None):
    """处理503错误"""
    error_503_handler.record_error('503', url, page_number)
    
    # 获取等待时间
    wait_time = error_503_handler.get_wait_time('503')
    
    # 检查是否应该停止
    should_stop = error_503_handler.should_stop_crawling()
    
    # 获取恢复建议
    suggestions = []
    if error_503_handler.should_change_proxy():
        suggestions.append("更换代理")
    if error_503_handler.should_change_user_agent():
        suggestions.append("更换用户代理")
    if error_503_handler.should_reduce_concurrency():
        suggestions.append("降低并发数")
    
    return {
        'should_stop': should_stop,
        'wait_time': wait_time,
        'suggestions': suggestions,
        'consecutive_count': error_503_handler.consecutive_503_count,
        'recovery_mode': error_503_handler.recovery_mode
    }

if __name__ == "__main__":
    # 测试503错误处理器
    handler = Error503Handler()
    
    print("测试503错误处理器")
    print("=" * 50)
    
    # 模拟一些503错误
    for i in range(5):
        handler.record_error('503', f'test_url_{i}', i+1)
        result = handle_503_error(f'test_url_{i}', i+1)
        
        print(f"第{i+1}次503错误:")
        print(f"  等待时间: {result['wait_time']:.1f}秒")
        print(f"  是否停止: {result['should_stop']}")
        print(f"  建议: {', '.join(result['suggestions'])}")
        print(f"  连续次数: {result['consecutive_count']}")
        print()
    
    # 显示统计信息
    stats = handler.get_stats()
    print("统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
