#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon反503错误配置
专门针对503 Service Unavailable错误的优化配置
"""

# 反503错误配置
ANTI_503_CONFIG = {
    # 页数限制
    'max_safe_pages': 50,           # 安全的最大页数
    'high_page_threshold': 20,      # 高页数阈值
    'max_deep_pages': 100,          # 深度抓取最大页数
    
    # 延迟策略
    'base_delay_range': (3, 8),     # 基础页面间延迟（秒）
    'high_page_delay_multiplier': 2, # 高页数延迟倍数
    'max_delay_multiplier': 5,      # 最大延迟倍数
    
    # 503错误处理
    'error_503_delay_base': 60,     # 503错误基础延迟（秒）
    'error_503_delay_max': 300,     # 503错误最大延迟（秒）
    'error_503_max_retries': 3,     # 503错误最大重试次数
    'error_503_backoff_factor': 2,  # 503错误退避因子
    
    # 连续错误处理
    'max_consecutive_errors': 3,    # 最大连续错误数
    'consecutive_error_delay': 30,  # 连续错误延迟（秒）
    
    # 请求频率控制
    'requests_per_minute': 10,      # 每分钟最大请求数
    'burst_requests': 3,            # 突发请求数
    'burst_interval': 60,           # 突发间隔（秒）
    
    # 代理轮换策略
    'proxy_rotation_interval': 5,   # 代理轮换间隔（请求数）
    'proxy_failure_threshold': 2,   # 代理失败阈值
    'proxy_cooldown_time': 300,     # 代理冷却时间（秒）
    
    # 用户代理轮换
    'ua_rotation_interval': 3,      # UA轮换间隔（请求数）
    'ua_pool_size': 20,             # UA池大小
    
    # 会话管理
    'session_lifetime': 1800,       # 会话生命周期（秒）
    'session_request_limit': 100,   # 每个会话最大请求数
    
    # 检测和恢复
    'health_check_interval': 300,   # 健康检查间隔（秒）
    'recovery_delay': 600,          # 恢复延迟（秒）
    'auto_recovery_enabled': True,  # 是否启用自动恢复
}

# 页面抓取策略
PAGE_STRATEGY = {
    # 分批抓取策略
    'batch_size': 10,               # 每批抓取页数
    'batch_interval': 300,          # 批次间隔（秒）
    'max_batches_per_task': 5,      # 每个任务最大批次数
    
    # 优先级策略
    'priority_pages': 20,           # 优先抓取页数
    'priority_delay': (2, 4),       # 优先页面延迟
    'normal_delay': (5, 10),        # 普通页面延迟
    'deep_delay': (10, 20),         # 深度页面延迟
    
    # 质量控制
    'min_products_per_page': 5,     # 每页最少商品数
    'max_empty_pages': 3,           # 最大空页面数
    'quality_threshold': 0.8,       # 质量阈值
}

# 错误恢复策略
ERROR_RECOVERY = {
    # 503错误恢复
    '503': {
        'initial_delay': 60,        # 初始延迟
        'max_delay': 600,           # 最大延迟
        'backoff_factor': 2,        # 退避因子
        'max_retries': 5,           # 最大重试次数
        'recovery_actions': [       # 恢复动作
            'change_proxy',
            'change_user_agent',
            'increase_delay',
            'reduce_concurrency'
        ]
    },
    
    # 429错误恢复
    '429': {
        'initial_delay': 120,
        'max_delay': 1800,
        'backoff_factor': 3,
        'max_retries': 3,
        'recovery_actions': [
            'change_proxy',
            'long_delay',
            'reduce_frequency'
        ]
    },
    
    # 验证码恢复
    'captcha': {
        'initial_delay': 300,
        'max_delay': 1800,
        'backoff_factor': 2,
        'max_retries': 2,
        'recovery_actions': [
            'change_proxy',
            'change_user_agent',
            'long_delay'
        ]
    }
}

# 监控和告警
MONITORING = {
    # 错误率监控
    'error_rate_threshold': 0.3,    # 错误率阈值
    'error_rate_window': 100,       # 错误率统计窗口
    
    # 503错误监控
    '503_rate_threshold': 0.1,      # 503错误率阈值
    '503_consecutive_threshold': 3, # 连续503错误阈值
    
    # 性能监控
    'response_time_threshold': 30,  # 响应时间阈值（秒）
    'success_rate_threshold': 0.7,  # 成功率阈值
    
    # 告警配置
    'alert_enabled': True,          # 是否启用告警
    'alert_interval': 300,          # 告警间隔（秒）
    'alert_methods': ['log', 'console']  # 告警方式
}

def get_delay_for_page(page_number):
    """根据页数获取延迟时间"""
    if page_number <= PAGE_STRATEGY['priority_pages']:
        return PAGE_STRATEGY['priority_delay']
    elif page_number <= ANTI_503_CONFIG['high_page_threshold']:
        return PAGE_STRATEGY['normal_delay']
    else:
        return PAGE_STRATEGY['deep_delay']

def get_503_delay(attempt_count):
    """获取503错误的延迟时间"""
    base_delay = ANTI_503_CONFIG['error_503_delay_base']
    max_delay = ANTI_503_CONFIG['error_503_delay_max']
    backoff_factor = ANTI_503_CONFIG['error_503_backoff_factor']
    
    delay = base_delay * (backoff_factor ** (attempt_count - 1))
    return min(delay, max_delay)

def should_stop_on_error(error_type, consecutive_count):
    """判断是否应该因错误停止"""
    if error_type == '503':
        return consecutive_count >= ANTI_503_CONFIG['error_503_max_retries']
    elif error_type == 'consecutive':
        return consecutive_count >= ANTI_503_CONFIG['max_consecutive_errors']
    else:
        return consecutive_count >= 5

def get_recovery_actions(error_type):
    """获取错误恢复动作"""
    return ERROR_RECOVERY.get(error_type, {}).get('recovery_actions', [])

def is_high_risk_page(page_number):
    """判断是否为高风险页面"""
    return page_number > ANTI_503_CONFIG['high_page_threshold']

def get_safe_page_limit():
    """获取安全页面限制"""
    return ANTI_503_CONFIG['max_safe_pages']

def get_request_delay(page_number, error_count=0):
    """获取请求延迟时间"""
    base_delay = get_delay_for_page(page_number)
    
    # 根据错误数增加延迟
    if error_count > 0:
        error_multiplier = min(error_count * 0.5 + 1, 3)
        base_delay = (base_delay[0] * error_multiplier, base_delay[1] * error_multiplier)
    
    # 高页数增加延迟
    if is_high_risk_page(page_number):
        high_page_multiplier = min(page_number / ANTI_503_CONFIG['high_page_threshold'], 
                                  ANTI_503_CONFIG['max_delay_multiplier'])
        base_delay = (base_delay[0] * high_page_multiplier, base_delay[1] * high_page_multiplier)
    
    return base_delay

# 使用示例
if __name__ == "__main__":
    print("Amazon反503错误配置")
    print("=" * 50)
    
    # 测试不同页数的延迟
    test_pages = [1, 5, 10, 20, 30, 50, 100]
    
    print("页数延迟策略:")
    for page in test_pages:
        delay = get_request_delay(page)
        risk = "高风险" if is_high_risk_page(page) else "正常"
        print(f"  第{page:3d}页: {delay[0]:4.1f}-{delay[1]:4.1f}秒 ({risk})")
    
    print("\n503错误延迟策略:")
    for attempt in range(1, 6):
        delay = get_503_delay(attempt)
        print(f"  第{attempt}次重试: {delay:6.1f}秒")
    
    print(f"\n安全页面限制: {get_safe_page_limit()}页")
    print(f"高风险页面阈值: {ANTI_503_CONFIG['high_page_threshold']}页")
