import pymysql

# === 配置 ===
DB_CONFIG = {
    'host': '**************',
    'user': 'root',
    'password': 'xh884813@@@XH',
    'database': 'xace200_lsh',
    'port': 22888
}

def check_amazon_progress():
    """检查Amazon类目同步进度"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print("=== Amazon 类目同步进度 ===")
    
    # 总体统计
    cursor.execute("SELECT COUNT(*) FROM zz_amazon_category WHERE site = 'US'")
    total = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_amazon_category WHERE site = 'US' AND leaf_category = 1")
    leaf_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_amazon_category WHERE site = 'US' AND leaf_category != 1")
    non_leaf_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_amazon_category WHERE site = 'US' AND leaf_category != 1 AND update_status = 1")
    processed_non_leaf = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_amazon_category WHERE site = 'US' AND leaf_category != 1 AND (update_status IS NULL OR update_status != 1)")
    unprocessed_non_leaf = cursor.fetchone()[0]
    
    print(f"总类目数: {total}")
    print(f"叶子类目数: {leaf_count}")
    print(f"非叶子类目数: {non_leaf_count}")
    print(f"已完全处理的非叶子类目: {processed_non_leaf}")
    print(f"未完全处理的非叶子类目: {unprocessed_non_leaf}")
    print(f"处理进度: {(processed_non_leaf/non_leaf_count*100):.2f}%" if non_leaf_count > 0 else "100%")
    
    # 检查孤儿节点
    cursor.execute("""
        SELECT COUNT(*) FROM zz_amazon_category a
        WHERE site = 'US'
        AND category_parent_id IS NOT NULL 
        AND category_parent_id != 0
        AND NOT EXISTS (
            SELECT 1 FROM zz_amazon_category b 
            WHERE b.site = 'US' AND b.category_id = a.category_parent_id
        )
    """)
    orphan_count = cursor.fetchone()[0]
    if orphan_count > 0:
        print(f"⚠️  发现 {orphan_count} 个孤儿节点（父节点缺失）")
    
    # 层级分布
    print("\n=== 层级分布 ===")
    cursor.execute("""
        SELECT 
            CHAR_LENGTH(node_path_id) - CHAR_LENGTH(REPLACE(node_path_id, '>', '')) + 1 as level,
            COUNT(*) as count
        FROM zz_amazon_category 
        WHERE site = 'US' AND node_path_id IS NOT NULL
        GROUP BY level
        ORDER BY level
    """)
    
    for level, count in cursor.fetchall():
        print(f"第 {level} 层: {count} 个类目")
    
    # 未处理的父类目详情
    print("\n=== 未完全处理的父类目（前10个）===")
    cursor.execute("""
        SELECT a.category_id, a.category_name, a.chinese_name,
               (SELECT COUNT(*) FROM zz_amazon_category b 
                WHERE b.category_parent_id = a.category_id AND b.site = 'US') as child_count
        FROM zz_amazon_category a
        WHERE site = 'US' 
        AND leaf_category != 1 
        AND (update_status IS NULL OR update_status != 1)
        ORDER BY category_id
        LIMIT 10
    """)
    
    for cat_id, name, chinese_name, child_count in cursor.fetchall():
        print(f"ID: {cat_id}, 名称: {name}, 中文名: {chinese_name}, 已有子节点: {child_count}")
    
    cursor.close()
    conn.close()

def check_temu_progress():
    """检查Temu类目同步进度"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    print("\n=== Temu 类目同步进度 ===")
    
    # 总体统计
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category")
    total = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 1")
    leaf_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0")
    non_leaf_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0 AND has_update = 1")
    processed_non_leaf = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0 AND (has_update IS NULL OR has_update != 1)")
    unprocessed_non_leaf = cursor.fetchone()[0]
    
    print(f"总类目数: {total}")
    print(f"叶子类目数: {leaf_count}")
    print(f"非叶子类目数: {non_leaf_count}")
    print(f"已完全处理的非叶子类目: {processed_non_leaf}")
    print(f"未完全处理的非叶子类目: {unprocessed_non_leaf}")
    print(f"处理进度: {(processed_non_leaf/non_leaf_count*100):.2f}%" if non_leaf_count > 0 else "100%")
    
    # 检查孤儿节点
    cursor.execute("""
        SELECT COUNT(*) FROM zz_temu_category a
        WHERE parent_cat_id IS NOT NULL 
        AND parent_cat_id != 0
        AND NOT EXISTS (
            SELECT 1 FROM zz_temu_category b 
            WHERE b.cat_id = a.parent_cat_id
        )
    """)
    orphan_count = cursor.fetchone()[0]
    if orphan_count > 0:
        print(f"⚠️  发现 {orphan_count} 个孤儿节点（父节点缺失）")
    
    # 层级分布
    print("\n=== 层级分布 ===")
    cursor.execute("""
        SELECT cat_level, COUNT(*) as count
        FROM zz_temu_category 
        GROUP BY cat_level
        ORDER BY cat_level
    """)
    
    for level, count in cursor.fetchall():
        print(f"第 {level} 层: {count} 个类目")
    
    # 未处理的父类目详情
    print("\n=== 未完全处理的父类目（前10个）===")
    cursor.execute("""
        SELECT a.cat_id, a.cat_name,
               (SELECT COUNT(*) FROM zz_temu_category b 
                WHERE b.parent_cat_id = a.cat_id) as child_count
        FROM zz_temu_category a
        WHERE is_leaf = 0 
        AND (has_update IS NULL OR has_update != 1)
        ORDER BY cat_id
        LIMIT 10
    """)
    
    for cat_id, name, child_count in cursor.fetchall():
        print(f"ID: {cat_id}, 名称: {name}, 已有子节点: {child_count}")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    check_amazon_progress()
    check_temu_progress()
