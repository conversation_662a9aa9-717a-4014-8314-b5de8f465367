-- 创建Amazon商品数据库表结构

-- 1. 创建SPU表（父体商品信息表）- 添加新字段
CREATE TABLE IF NOT EXISTS `zz_amazon_products_spu` (
  `asin` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '真正的父ASIN (Parent ASIN)，作为主键',
  `title` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品标题',
  `brand` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品牌名',
  `rating` decimal(3,2) DEFAULT NULL COMMENT '评分，如 4.50',
  `review_count` int DEFAULT NULL COMMENT '评价数量',
  `main_image_url` text COLLATE utf8mb4_general_ci COMMENT '首图链接 (高清图)',
  `image_urls` text COLLATE utf8mb4_general_ci COMMENT '所有图片链接集合 (JSON数组格式)',
  `bullet_points` text COLLATE utf8mb4_general_ci COMMENT '商品简要描述/五点描述 (JSON数组格式)',
  `description` text COLLATE utf8mb4_general_ci COMMENT '商品长描述 (可以是HTML或纯文本)',
  `product_details` text COLLATE utf8mb4_general_ci COMMENT '产品参数/详情表格 (JSON格式)',
  `product_attributes` text COLLATE utf8mb4_general_ci COMMENT '产品参数属性 (JSON格式，包含技术规格等)',
  `temu_product_detail` text COLLATE utf8mb4_general_ci COMMENT 'Temu格式的产品详情 (JSON格式)',
  `category_path` text COLLATE utf8mb4_general_ci COMMENT '亚马逊类目路径',
  `first_crawled_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '首次创建时间',
  `last_crawled_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`asin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='亚马逊产品SPU（父体）信息表';

-- 2. 创建SKU表（变体商品信息表）
CREATE TABLE IF NOT EXISTS `zz_amazon_products_sku` (
  `sku_id` int NOT NULL AUTO_INCREMENT,
  `asin` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '子体ASIN (Child ASIN)',
  `parent_asin` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的父体ASIN',
  `price` decimal(10,2) DEFAULT NULL COMMENT '价格',
  `currency` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货币符号',
  `stock_status` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '库存状态，如 In Stock',
  `image_url` text COLLATE utf8mb4_general_ci COMMENT '变体专属图片链接',
  `variation_attributes` text COLLATE utf8mb4_general_ci COMMENT '变体属性 (JSON格式, 如 {"Color": "Red", "Size": "M"})',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已失效（0=有效, 1=已失效）',
  `last_crawled_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`sku_id`),
  UNIQUE KEY `uk_child_asin` (`asin`),
  KEY `idx_parent_asin` (`parent_asin`),
  CONSTRAINT `fk_sku_to_spu` FOREIGN KEY (`parent_asin`) REFERENCES `zz_amazon_products_spu` (`asin`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='亚马逊产品SKU（变体）信息表';

-- 3. 插入测试数据（基于我们刚才提取的数据）
INSERT INTO `zz_amazon_products_spu` 
(`asin`, `title`, `brand`, `rating`, `review_count`, `main_image_url`, `image_urls`, `bullet_points`, `description`, `product_details`, `category_path`)
VALUES 
('B0DLNQLB5B', 
 'Aenebe Electric Bead Spinner, Clay Bead Spinner for Jewelry Making, Bracelet Spinner and Necklace Making Machine with 2PCS Needles, DIY Set Gift(Beads Not Included)', 
 'Aenebe', 
 4.10, 
 37, 
 'https://m.media-amazon.com/images/I/71WgfpGC5iL.__AC_SX300_SY300_QL70_FMwebp_.jpg',
 '["https://m.media-amazon.com/images/I/41VQIFMFgAL._AC_US40_.jpg", "https://m.media-amazon.com/images/I/51paUog3UbL._SS64_.jpg", "https://m.media-amazon.com/images/I/51yGDjLEJRL._SS64_.jpg", "https://m.media-amazon.com/images/I/51WPPwv2PuL._SS64_.jpg"]',
 '["【Fast and Efficient Beading】 The electric bead spinner is designed for flat clay beads to improve the efficiency of beading.Compared with manual production, the efficiency of bead spinning is increased by 50%. Thanks to the high motor efficiency, the bracelet will be made in less than 2 minutes", "【Multi-Functional Compatibility】 Upgrade the motor, so that the ball bowl noise is low, more stable rotation. The divider bar in the bowl ensures smooth rotation and successful bead placement. The smaller bowl opening prevents the beads from flying out during use", "【Beginner Friendly】 Whether you are a beginner or an experienced user, clay bead spinner can enhance your beading experience and help you create beautiful jewelry", "【Easy to Use and Fun】fill the bowl with your favorite clay beads 1/3 of the way, then thread through the needle, secure it in the holder slot, activate the switch, and insert the needle into the bowl. Adjust the position of the needle until the bead slides smoothly onto the needle", "【Creative Gift Ideas】 -Aenebe bead spinner is a unique gift for girls and craft lovers, making it an ideal birthday gift. Because of its lightweight and compact size, it is more convenient to use or carry, suitable for outdoor or group activities,Package List: Clay bead spinner x1, Needle base x1,U-shape needles x2, Exquisite gift box x1,Cartoon Stickers x2"]',
 'Electric Bead Spinner Electric Bead Spinner for Jewelry Making Seed Bead Spinner Bracelet Spinner and Necklace Making Machine',
 '{}',
 'Arts, Crafts & Sewing > Beading & Jewelry Making > Beading Supplies > Bead Looms')
ON DUPLICATE KEY UPDATE
title = VALUES(title),
brand = VALUES(brand),
rating = VALUES(rating),
review_count = VALUES(review_count),
main_image_url = VALUES(main_image_url),
image_urls = VALUES(image_urls),
bullet_points = VALUES(bullet_points),
description = VALUES(description),
product_details = VALUES(product_details),
category_path = VALUES(category_path),
last_crawled_at = CURRENT_TIMESTAMP;

-- 4. 插入SKU测试数据
INSERT INTO `zz_amazon_products_sku` 
(`asin`, `parent_asin`, `price`, `currency`, `stock_status`, `image_url`, `variation_attributes`)
VALUES 
('B0DLNVJM3R', 'B0DLNQLB5B', 9.99, 'USD', 'In Stock', 'https://m.media-amazon.com/images/I/51paUog3UbL._SS64_.jpg', '{"Color": "Blue Bead Spinner Kit"}'),
('B0DLNSW59D', 'B0DLNQLB5B', 9.99, 'USD', 'In Stock', 'https://m.media-amazon.com/images/I/51yGDjLEJRL._SS64_.jpg', '{"Color": "Light Pink Bead Spinner"}'),
('B0DLNQLB5B', 'B0DLNQLB5B', 9.99, 'USD', 'In Stock', 'https://m.media-amazon.com/images/I/41VQIFMFgAL._SS64_.jpg', '{"Color": "Peach Pink Bead Spinner"}'),
('B0DLNR3YSP', 'B0DLNQLB5B', 9.99, 'USD', 'In Stock', 'https://m.media-amazon.com/images/I/51WPPwv2PuL._SS64_.jpg', '{"Color": "Pink Bead Spinner Kit"}')
ON DUPLICATE KEY UPDATE
price = VALUES(price),
currency = VALUES(currency),
stock_status = VALUES(stock_status),
image_url = VALUES(image_url),
variation_attributes = VALUES(variation_attributes),
last_crawled_at = CURRENT_TIMESTAMP;

-- 5. 查询验证数据
SELECT 
    s.asin as spu_asin,
    s.title,
    s.brand,
    s.rating,
    s.review_count,
    COUNT(k.sku_id) as sku_count
FROM zz_amazon_products_spu s
LEFT JOIN zz_amazon_products_sku k ON s.asin = k.parent_asin
WHERE s.asin = 'B0DLNQLB5B'
GROUP BY s.asin;

-- 6. 查询SKU详情
SELECT 
    sku_id,
    asin as sku_asin,
    parent_asin,
    price,
    currency,
    stock_status,
    variation_attributes
FROM zz_amazon_products_sku 
WHERE parent_asin = 'B0DLNQLB5B'
ORDER BY sku_id;
