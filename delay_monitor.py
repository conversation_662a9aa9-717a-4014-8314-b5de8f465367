#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
延迟监控工具
实时监控和显示动态延迟调整情况
"""

import time
import threading
from datetime import datetime
from dynamic_delay_manager import dynamic_delay_manager

class DelayMonitor:
    """延迟监控器"""
    
    def __init__(self):
        self.running = False
        self.monitor_thread = None
        self.last_config = None
        
    def start_monitoring(self, interval=60):
        """启动延迟监控"""
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"📊 延迟监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        print("🛑 延迟监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.running:
            try:
                self.display_delay_status()
                time.sleep(interval)
            except Exception as e:
                print(f"❌ 延迟监控出错: {e}")
                time.sleep(interval)
    
    def display_delay_status(self):
        """显示延迟状态"""
        config = dynamic_delay_manager.get_current_config()
        
        print("\n" + "=" * 80)
        print(f"⏱️ 延迟监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 1. 显示当前延迟配置
        self.display_current_delays(config)
        
        # 2. 显示错误率统计
        self.display_error_rates(config)
        
        # 3. 显示调整历史
        self.display_adjustment_history(config)
        
        # 4. 显示延迟建议
        self.display_recommendations()
        
        # 5. 检查配置变化
        self.check_config_changes(config)
        
        print("=" * 80)
    
    def display_current_delays(self, config):
        """显示当前延迟配置"""
        print("\n⚙️ 当前延迟配置:")
        
        multiplier = config['current_multiplier']
        print(f"  延迟倍数: {multiplier:.1f}x")
        
        delays = config['delays']
        print("  延迟范围:")
        
        delay_types = {
            'request': '请求间隔',
            'page': '页面间隔', 
            'task': '任务间隔',
            'error': '错误延迟'
        }
        
        for delay_type, name in delay_types.items():
            if delay_type in delays:
                min_delay = delays[delay_type]['min']
                max_delay = delays[delay_type]['max']
                print(f"    {name}: {min_delay:.1f}-{max_delay:.1f}秒")
    
    def display_error_rates(self, config):
        """显示错误率统计"""
        print("\n📊 错误率统计:")
        
        stats = config['stats']
        recent_error_rate = config['recent_error_rate']
        recent_503_rate = config['recent_503_rate']
        
        total_requests = stats['total_requests']
        total_503_errors = stats['total_503_errors']
        
        print(f"  总请求数: {total_requests}")
        print(f"  总503错误: {total_503_errors}")
        
        if total_requests > 0:
            overall_503_rate = total_503_errors / total_requests
            print(f"  整体503错误率: {overall_503_rate:.1%}")
        
        print(f"  最近错误率: {recent_error_rate:.1%}")
        print(f"  最近503错误率: {recent_503_rate:.1%}")
        
        # 错误率状态指示
        if recent_503_rate > 0.2:
            status = "🔴 严重"
        elif recent_503_rate > 0.1:
            status = "🟡 警告"
        elif recent_503_rate > 0.05:
            status = "🟠 注意"
        else:
            status = "🟢 正常"
        
        print(f"  状态: {status}")
    
    def display_adjustment_history(self, config):
        """显示调整历史"""
        print("\n📈 调整历史:")
        
        stats = config['stats']
        total_adjustments = stats['total_adjustments']
        last_adjustment_time = stats['last_adjustment_time']
        
        print(f"  总调整次数: {total_adjustments}")
        
        if last_adjustment_time:
            time_since_last = (datetime.now() - last_adjustment_time).total_seconds()
            if time_since_last < 60:
                time_str = f"{time_since_last:.0f}秒前"
            elif time_since_last < 3600:
                time_str = f"{time_since_last/60:.0f}分钟前"
            else:
                time_str = f"{time_since_last/3600:.1f}小时前"
            
            print(f"  最后调整: {time_str}")
        else:
            print("  最后调整: 无")
    
    def display_recommendations(self):
        """显示延迟建议"""
        print("\n💡 延迟优化建议:")
        
        recommendations = dynamic_delay_manager.get_recommendations()
        
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    def check_config_changes(self, config):
        """检查配置变化"""
        if self.last_config is None:
            self.last_config = config
            return
        
        # 检查延迟倍数变化
        old_multiplier = self.last_config['current_multiplier']
        new_multiplier = config['current_multiplier']
        
        if abs(new_multiplier - old_multiplier) > 0.1:
            if new_multiplier > old_multiplier:
                print(f"\n🔺 延迟倍数增加: {old_multiplier:.1f}x → {new_multiplier:.1f}x")
            else:
                print(f"\n🔻 延迟倍数减少: {old_multiplier:.1f}x → {new_multiplier:.1f}x")
        
        # 检查503错误率变化
        old_503_rate = self.last_config['recent_503_rate']
        new_503_rate = config['recent_503_rate']
        
        if abs(new_503_rate - old_503_rate) > 0.05:  # 5%变化阈值
            if new_503_rate > old_503_rate:
                print(f"📈 503错误率上升: {old_503_rate:.1%} → {new_503_rate:.1%}")
            else:
                print(f"📉 503错误率下降: {old_503_rate:.1%} → {new_503_rate:.1%}")
        
        self.last_config = config
    
    def get_current_status(self):
        """获取当前状态摘要"""
        config = dynamic_delay_manager.get_current_config()
        
        return {
            'multiplier': config['current_multiplier'],
            'recent_503_rate': config['recent_503_rate'],
            'total_requests': config['stats']['total_requests'],
            'total_adjustments': config['stats']['total_adjustments'],
            'status': self._get_status_level(config['recent_503_rate'])
        }
    
    def _get_status_level(self, error_rate):
        """获取状态级别"""
        if error_rate > 0.2:
            return "严重"
        elif error_rate > 0.1:
            return "警告"
        elif error_rate > 0.05:
            return "注意"
        else:
            return "正常"

def show_delay_dashboard():
    """显示延迟仪表板"""
    print("🚀 Amazon爬虫延迟监控仪表板")
    print("=" * 60)
    
    monitor = DelayMonitor()
    
    try:
        # 显示当前状态
        monitor.display_delay_status()
        
        print("\n📋 监控功能:")
        print("1. 实时延迟配置监控")
        print("2. 503错误率跟踪")
        print("3. 延迟调整历史")
        print("4. 智能优化建议")
        print("\n选择操作:")
        print("1. 启动实时监控")
        print("2. 显示当前状态")
        print("3. 手动增加延迟")
        print("4. 重置延迟配置")
        print("5. 退出")
        
        while True:
            choice = input("\n请选择 (1-5): ").strip()
            
            if choice == '1':
                print("启动实时监控...")
                monitor.start_monitoring(interval=30)
                input("按回车键停止监控...")
                monitor.stop_monitoring()
                
            elif choice == '2':
                monitor.display_delay_status()
                
            elif choice == '3':
                try:
                    multiplier = float(input("输入延迟倍数 (1.0-5.0): "))
                    if 1.0 <= multiplier <= 5.0:
                        dynamic_delay_manager.force_increase_delays(multiplier)
                        print(f"✅ 延迟已手动调整为 {multiplier}x")
                    else:
                        print("❌ 倍数必须在1.0-5.0之间")
                except ValueError:
                    print("❌ 请输入有效的数字")
                    
            elif choice == '4':
                dynamic_delay_manager.reset_to_base_delays()
                print("✅ 延迟配置已重置")
                
            elif choice == '5':
                break
                
            else:
                print("❌ 无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n收到中断信号...")
        monitor.stop_monitoring()
    
    except Exception as e:
        print(f"❌ 仪表板出错: {e}")
        monitor.stop_monitoring()

def main():
    """主函数"""
    show_delay_dashboard()

if __name__ == "__main__":
    main()
