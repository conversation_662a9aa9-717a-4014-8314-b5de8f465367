import pymysql
import requests
import time
import random

# === 配置 ===
DB_CONFIG = {
    'host': '**************',
    'user': 'root',
    'password': 'xh884813@@@XH',
    'database': 'xace200_lsh',
    'port': 22888
}

# 测试模式：设置为 True 时只插入一条数据进行测试
TEST_MODE = False

BASE_URL = "https://www.dianxiaomi.com/popTemuCategory/list.json"
HEADERS = {
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "origin": "https://www.dianxiaomi.com",
    "priority": "u=1, i",
    "referer": "https://www.dianxiaomi.com/userTemplate/popTemu/edit.htm?id=780326",
    "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}
COOKIES = {
    "_dxm_ad_client_id": "5846E6F2F838D4287F85B29DFAA73CBBF",
    "MYJ_MKTG_fapsc5t4tc": "JTdCJTdE",
    "dxm_i": "MjA0ODYyNiFhVDB5TURRNE5qSTIhYmQyZmNjNDc0NDBmOTQ2YTM4MGNmOTUwMjkzZmI4ZGY",
    "dxm_t": "********************************************************************************",
    "dxm_c": "aGVYZ0NDb0MhWXoxb1pWaG5RME52UXchZmM0N2ZmMThjYTcxMDhmN2U3ZTAzN2UyOGNhMjVkZTQ",
    "dxm_w": "NWQ2MGUzZGJiMjlmMzk3OTRlMDU1MDgwYjM1NjM1NTEhZHowMVpEWXdaVE5rWW1JeU9XWXpPVGM1TkdVd05UVXdPREJpTXpVMk16VTFNUSFmNDk3ODQwMmQxNjEzMDFjOTA5NjIyOWVlZGJjMTAwOA",
    "dxm_s": "T2ZJnQVt09Mdlu038RQ0Dcab6cHVMp0oNn3CJdN3UdI",
    "Hm_lvt_f8001a3f3d9bf5923f780580eb550c0b": "**********,**********,**********,**********",
    "HMACCOUNT": "BE7D75DAE6DD20AD",
    "_clck": "41l4xg%7C2%7Cfwr%7C0%7C1983",
    "Hm_lpvt_f8001a3f3d9bf5923f780580eb550c0b": "**********",
    "_clsk": "jttq99%7C**********800%7C13%7C0%7Cs.clarity.ms%2Fcollect",
    "JSESSIONID": "385BC433EF6F175B4DFD5B2B6172FCC4",
    # 建议更新完整有效 cookie
}

SHOP_ID = "6959965"

# 新增：断点续传配置
ENABLE_RESUME = True  # 是否启用断点续传
MAX_RETRY = 3  # 最大重试次数

# === 数据库操作函数 ===
def get_existing_categories():
    """获取数据库中已存在的类目ID"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    cursor.execute("SELECT DISTINCT cat_id FROM zz_temu_category")
    existing_ids = {row[0] for row in cursor.fetchall()}
    cursor.close()
    conn.close()
    return existing_ids

def get_unprocessed_parent_ids():
    """获取需要处理的父类目ID（非叶子节点且可能还有未处理的子节点）"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    # 查找所有非叶子节点但 has_update != 1 的类目
    sql = """
        SELECT DISTINCT cat_id 
        FROM zz_temu_category 
        WHERE is_leaf = 0 
        AND (has_update IS NULL OR has_update != 1)
        ORDER BY cat_id
    """
    
    cursor.execute(sql)
    unprocessed_ids = [row[0] for row in cursor.fetchall()]
    cursor.close()
    conn.close()
    return unprocessed_ids

def mark_category_processed(category_id):
    """标记类目已处理"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE zz_temu_category SET has_update = 1 WHERE cat_id = %s",
        (category_id,)
    )
    conn.commit()
    cursor.close()
    conn.close()

def get_progress_info():
    """获取当前进度信息"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category")
    total_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 1")
    leaf_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE has_update = 1")
    processed_count = cursor.fetchone()[0]
    
    cursor.close()
    conn.close()
    
    return {
        'total': total_count,
        'leaf': leaf_count,
        'processed': processed_count,
        'unprocessed': total_count - processed_count
    }

def check_category_completion_status():
    """检查类目处理完成状态，返回详细统计"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    stats = {}
    
    # 总类目数
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category")
    stats['total'] = cursor.fetchone()[0]
    
    # 叶子节点数
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 1")
    stats['leaf_nodes'] = cursor.fetchone()[0]
    
    # 非叶子节点数
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0")
    stats['non_leaf_nodes'] = cursor.fetchone()[0]
    
    # 已标记为处理完成的非叶子节点
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0 AND has_update = 1")
    stats['processed_non_leaf'] = cursor.fetchone()[0]
    
    # 未标记为处理完成的非叶子节点
    cursor.execute("SELECT COUNT(*) FROM zz_temu_category WHERE is_leaf = 0 AND (has_update IS NULL OR has_update != 1)")
    stats['unprocessed_non_leaf'] = cursor.fetchone()[0]
    
    # 检查孤儿节点
    cursor.execute("""
        SELECT COUNT(*) FROM zz_temu_category a
        WHERE parent_cat_id IS NOT NULL 
        AND parent_cat_id != 0
        AND NOT EXISTS (
            SELECT 1 FROM zz_temu_category b 
            WHERE b.cat_id = a.parent_cat_id
        )
    """)
    stats['orphan_nodes'] = cursor.fetchone()[0]
    
    cursor.close()
    conn.close()
    return stats

def find_incomplete_parent_nodes():
    """查找可能不完整的父节点"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    sql = """
        SELECT cat_id, cat_name, 
               (SELECT COUNT(*) FROM zz_temu_category b 
                WHERE b.parent_cat_id = a.cat_id) as child_count
        FROM zz_temu_category a
        WHERE is_leaf = 0 
        AND (has_update IS NULL OR has_update != 1)
        ORDER BY cat_id
    """
    
    cursor.execute(sql)
    incomplete_nodes = cursor.fetchall()
    cursor.close()
    conn.close()
    return incomplete_nodes

# === 插入数据 ===
def insert_category(cat):
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    # 检查是否已存在该类目
    cat_id = cat.get("catId")
    
    cursor.execute(
        "SELECT COUNT(*) FROM zz_temu_category WHERE cat_id = %s", 
        (cat_id,)
    )
    
    if cursor.fetchone()[0] > 0:
        print(f"跳过重复类目: {cat.get('catName')} (cat_id: {cat_id})")
        cursor.close()
        conn.close()
        return False
    
    sql = '''
        INSERT INTO zz_temu_category (
            id, site_id, cat_id, cat_name, parent_cat_id,
            cat_level, cat_type, is_leaf, node_path_id, node_path,
            is_hidden, hidden_type, class_type, class_id,
            parent_class_id, related_class_ids, enabled_model_type,
            enabled_model, need_guide_file, has_update,
            deleted, create_time, update_time
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                  %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                  %s, %s, %s)
    '''
    values = (
        cat.get("id"),
        cat.get("siteId"),
        cat.get("catId"),
        cat.get("catName"),
        cat.get("parentCatId"),
        cat.get("catLevel"),
        cat.get("catType"),
        cat.get("isLeaf"),
        cat.get("nodePathId"),
        cat.get("nodePath"),
        cat.get("isHidden"),
        cat.get("hiddenType"),
        cat.get("classType"),
        cat.get("classId"),
        cat.get("parentClassId"),
        cat.get("relatedClassIds"),
        cat.get("enabledModelType"),
        cat.get("enabledModel"),
        cat.get("needGuideFile"),
        cat.get("hasUpdate"),
        cat.get("deleted"),
        cat.get("createTime"),
        cat.get("updateTime"),
    )
    cursor.execute(sql, values)
    conn.commit()
    cursor.close()
    conn.close()
    return True

# === 优化的递归抓取函数 ===
def fetch_and_store_categories(parent_id, existing_categories=None, retry_count=0):
    """
    递归抓取并存储类目数据
    :param parent_id: 父类目ID
    :param existing_categories: 已存在的类目ID集合
    :param retry_count: 重试次数
    """
    if existing_categories is None:
        existing_categories = get_existing_categories()
    
    # 如果启用断点续传，检查是否已经处理过这个父类目
    if ENABLE_RESUME and parent_id != 0:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT has_update FROM zz_temu_category WHERE cat_id = %s",
            (parent_id,)
        )
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result and result[0] == 1:
            print(f"跳过已处理的类目 ID: {parent_id}")
            return existing_categories
    
    payload = {
        "shopId": SHOP_ID,
        "categoryParentId": parent_id
    }
    
    if TEST_MODE:
        print(f"[测试模式] 请求参数: {payload}")
        print(f"[测试模式] 请求URL: {BASE_URL}")
    
    # 添加随机延时
    if parent_id != 0:
        delay = random.uniform(0.5, 1.5)
        if not TEST_MODE:
            print(f"等待 {delay:.2f} 秒...")
        time.sleep(delay)
    
    try:
        response = requests.post(BASE_URL, headers=HEADERS, cookies=COOKIES, data=payload, timeout=30)
        
        if TEST_MODE:
            print(f"[测试模式] 响应状态码: {response.status_code}")
            print(f"[测试模式] 响应内容前500字符: {response.text[:500]}")
        
        if response.status_code != 200:
            print(f"HTTP错误: {response.status_code}")
            if retry_count < MAX_RETRY:
                print(f"重试第 {retry_count + 1} 次...")
                time.sleep(2 ** retry_count)  # 指数退避
                return fetch_and_store_categories(parent_id, existing_categories, retry_count + 1)
            return existing_categories
        
        if not response.text.strip():
            print("错误: 响应内容为空")
            return existing_categories
        
        try:
            result = response.json()
        except requests.exceptions.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            if retry_count < MAX_RETRY:
                print(f"重试第 {retry_count + 1} 次...")
                time.sleep(2 ** retry_count)
                return fetch_and_store_categories(parent_id, existing_categories, retry_count + 1)
            return existing_categories
        
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        if retry_count < MAX_RETRY:
            print(f"重试第 {retry_count + 1} 次...")
            time.sleep(2 ** retry_count)
            return fetch_and_store_categories(parent_id, existing_categories, retry_count + 1)
        return existing_categories

    if result.get("code") != 0:
        print("API错误:", result.get("msg"))
        return existing_categories

    categories = result.get("data", [])
    
    if TEST_MODE:
        print(f"[测试模式] 获取到 {len(categories)} 个类目")
        if categories:
            print(f"[测试模式] 第一个类目数据: {categories[0]}")
    
    if TEST_MODE and categories:
        cat = categories[0]
        print(f"[测试模式] 插入类目: {cat.get('catName')} (ID: {cat.get('catId')})")
        try:
            insert_category(cat)
            print(f"[测试模式] 成功插入类目，测试完成")
        except Exception as e:
            print(f"[测试模式] 数据库插入失败: {e}")
        return existing_categories
    
    # 处理所有类目
    new_categories = []
    all_api_categories = []  # API返回的所有子类目
    
    for cat in categories:
        cat_id = cat.get('catId')
        all_api_categories.append(cat)  # 记录API返回的所有子类目
        
        # 检查是否已存在
        if cat_id in existing_categories:
            print(f"跳过已存在的类目: {cat.get('catName')} (ID: {cat_id})")
            continue
        
        print(f"插入类目: {cat.get('catName')} (ID: {cat_id})")
        try:
            # 使用修改后的insert_category函数，如果插入成功则添加到existing_categories
            if insert_category(cat):
                existing_categories.add(cat_id)
                new_categories.append(cat)
                print(f"✓ 成功插入类目 ID: {cat_id}")
            else:
                print(f"⚠ 类目已存在，跳过插入")
        except Exception as e:
            print(f"✗ 数据库插入失败: {e}")
            continue
    
    # 获取数据库中已存在的子类目
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    cursor.execute(
        "SELECT cat_id, is_leaf, cat_name FROM zz_temu_category WHERE parent_cat_id = %s",
        (parent_id,)
    )
    existing_children = cursor.fetchall()
    cursor.close()
    conn.close()
    
    # 输出调试信息
    print(f"父类目 {parent_id}: API返回 {len(all_api_categories)} 个子类目，数据库中已有 {len(existing_children)} 个子类目")
    
    # 收集所有需要处理的非叶子节点
    all_non_leaf_children = set()
    api_child_ids = {cat.get('catId') for cat in all_api_categories}
    
    # 1. 处理API返回的非叶子节点
    for cat in all_api_categories:
        if not cat.get("isLeaf", True):  # 非叶子节点
            all_non_leaf_children.add(cat.get('catId'))
    
    # 2. 处理数据库中已存在但API未返回的非叶子节点
    db_only_children = []
    for child_id, is_leaf, cat_name in existing_children:
        if is_leaf != 1:  # 非叶子节点
            # 检查这个子类目是否在API返回的列表中
            if child_id not in api_child_ids:
                print(f"发现数据库中存在但API未返回的子类目: {child_id} ({cat_name})")
                db_only_children.append((child_id, cat_name))
                
                # 验证这个子类目是否确实应该属于当前父类目
                verify_payload = {
                    "shopId": SHOP_ID,
                    "categoryParentId": child_id
                }
                try:
                    verify_response = requests.post(BASE_URL, headers=HEADERS, cookies=COOKIES, 
                                                  data=verify_payload, timeout=10)
                    if verify_response.status_code == 200:
                        verify_result = verify_response.json()
                        if verify_result.get("code") == 0:
                            # 如果能成功获取这个类目的子类目，说明这个类目是有效的
                            all_non_leaf_children.add(child_id)
                            print(f"  验证通过: {child_id} 是有效的非叶子类目")
                        else:
                            print(f"  验证失败: {child_id} 可能已不存在或无子类目")
                    else:
                        print(f"  验证请求失败: HTTP {verify_response.status_code}")
                    time.sleep(0.1)  # 避免请求过快
                except Exception as e:
                    print(f"  验证异常: {e}")
                    # 保守处理：如果验证失败，仍然尝试处理
                    all_non_leaf_children.add(child_id)
    
    if db_only_children:
        print(f"注意: 发现 {len(db_only_children)} 个数据库中存在但API未返回的子类目")
    
    # 标记当前父类目已处理
    if parent_id != 0:
        mark_category_processed(parent_id)
        print(f"✓ 标记父类目 {parent_id} 已处理")
    
    # 3. 递归处理所有非叶子子类目
    for child_id in all_non_leaf_children:
        print(f"检查子类目，ID: {child_id}")
        
        # 检查该子类目是否已完全处理
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT has_update FROM zz_temu_category WHERE cat_id = %s",
            (child_id,)
        )
        child_result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        # 如果子类目未完全处理，则递归处理
        if not child_result or child_result[0] != 1:
            print(f"处理子类目，ID: {child_id}")
            existing_categories = fetch_and_store_categories(child_id, existing_categories)
        else:
            print(f"子类目 {child_id} 已完全处理，跳过")
    
    return existing_categories

def resume_from_database():
    """从数据库断点续传"""
    print("=== 断点续传模式 ===")
    
    # 获取详细的完成状态
    stats = check_category_completion_status()
    print(f"类目状态统计:")
    print(f"  总类目数: {stats['total']}")
    print(f"  叶子节点: {stats['leaf_nodes']}")
    print(f"  非叶子节点: {stats['non_leaf_nodes']}")
    print(f"  已处理完成的非叶子节点: {stats['processed_non_leaf']}")
    print(f"  未处理完成的非叶子节点: {stats['unprocessed_non_leaf']}")
    print(f"  孤儿节点（父节点缺失）: {stats['orphan_nodes']}")
    
    if stats['unprocessed_non_leaf'] == 0:
        print("所有非叶子节点都已处理完成！")
        return
    
    # 获取不完整的父节点详情
    incomplete_nodes = find_incomplete_parent_nodes()
    print(f"\n发现 {len(incomplete_nodes)} 个可能未完全处理的父类目:")
    
    for i, (node_id, node_name, child_count) in enumerate(incomplete_nodes[:10]):
        print(f"  {i+1}. ID: {node_id}, 名称: {node_name}, 已有子节点: {child_count}")
    
    if len(incomplete_nodes) > 10:
        print(f"  ... 还有 {len(incomplete_nodes) - 10} 个")
    
    if not incomplete_nodes:
        print("没有发现需要继续处理的类目")
        return
    
    existing_categories = get_existing_categories()
    print(f"\n当前数据库中有 {len(existing_categories)} 个类目")
    
    # 继续处理未完成的类目
    processed_count = 0
    for node_id, node_name, child_count in incomplete_nodes:
        print(f"\n[{processed_count + 1}/{len(incomplete_nodes)}] 继续处理: {node_name} (ID: {node_id}, 现有子节点: {child_count})")
        existing_categories = fetch_and_store_categories(node_id, existing_categories)
        processed_count += 1
        
        # 每处理5个类目显示一次进度
        if processed_count % 5 == 0:
            current_stats = check_category_completion_status()
            print(f"进度更新 - 已处理非叶子节点: {current_stats['processed_non_leaf']}/{current_stats['non_leaf_nodes']}")

# === 执行 ===
if __name__ == "__main__":
    if TEST_MODE:
        print("=== 测试模式启用 ===")
        print("只会插入一条数据进行测试...")
        fetch_and_store_categories(0)
        print("测试完成。如需正常运行，请将 TEST_MODE 设置为 False")
    else:
        # 检查是否需要断点续传
        progress = get_progress_info()
        
        if ENABLE_RESUME and progress['total'] > 0:
            print(f"检测到数据库中已有 {progress['total']} 个类目")
            choice = input("是否从断点继续？(y/n): ").lower().strip()
            
            if choice == 'y':
                resume_from_database()
            else:
                print("开始全新同步...")
                fetch_and_store_categories(0)
        else:
            print("=== 正常模式 ===")
            print("开始递归获取所有 Temu 类目...")
            fetch_and_store_categories(0)
        
        print("完成全部同步。")
        print("完成全部同步。")
