#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Prime信息为空的问题
分析原因并提供解决方案
"""

import pymysql
import sys
from datetime import datetime
from crawler_config import get_config

def analyze_prime_issue():
    """分析Prime信息为空的问题"""
    print("🔍 分析Prime信息为空的问题")
    print("=" * 60)
    
    print("📋 问题分析:")
    print("1. 离线测试显示Prime信息提取功能正常 (100%覆盖率)")
    print("2. 数据库中所有记录的Prime信息都为空 (100%空值)")
    print("3. 最近有204条新记录被成功保存")
    print("4. 日志显示大量503错误和代理连接问题")
    
    print("\n💡 可能原因:")
    print("1. 实际Amazon页面结构与本地HTML文件不同")
    print("2. Amazon返回的是简化版页面或反爬虫页面")
    print("3. Prime信息在JavaScript中动态加载")
    print("4. 页面被反爬虫机制拦截，返回不完整内容")
    
    return True

def check_recent_successful_pages():
    """检查最近成功抓取的页面特征"""
    print(f"\n📊 检查最近成功抓取的页面特征")
    print("=" * 60)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查看最近成功保存的记录
        cursor.execute("""
            SELECT 
                entry_asin,
                list_page_title,
                list_page_price,
                list_page_rating,
                list_page_review_count,
                list_page_main_image_url,
                list_page_prime_info,
                url,
                created_at
            FROM zz_amazon_page_tasks
            WHERE created_at > NOW() - INTERVAL 1 DAY
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        recent_records = cursor.fetchall()
        
        print(f"📋 最近10条记录分析:")
        
        field_stats = {
            'title': 0,
            'price': 0,
            'rating': 0,
            'review_count': 0,
            'image_url': 0,
            'prime_info': 0,
            'url': 0
        }
        
        for i, record in enumerate(recent_records, 1):
            print(f"\n--- 记录 {i} ---")
            print(f"ASIN: {record['entry_asin']}")
            print(f"时间: {record['created_at']}")
            
            # 检查各字段
            title_ok = bool(record['list_page_title'])
            price_ok = bool(record['list_page_price'] and record['list_page_price'] > 0)
            rating_ok = bool(record['list_page_rating'] and record['list_page_rating'] > 0)
            review_ok = bool(record['list_page_review_count'] and record['list_page_review_count'] > 0)
            image_ok = bool(record['list_page_main_image_url'])
            prime_ok = bool(record['list_page_prime_info'])
            url_ok = bool(record['url'] and 'amazon.com/dp/' in record['url'])
            
            print(f"标题: {'✅' if title_ok else '❌'} {record['list_page_title'][:30]}..." if record['list_page_title'] else "标题: ❌ (空)")
            print(f"价格: {'✅' if price_ok else '❌'} ${record['list_page_price']}" if record['list_page_price'] else "价格: ❌ (空)")
            print(f"评分: {'✅' if rating_ok else '❌'} {record['list_page_rating']}" if record['list_page_rating'] else "评分: ❌ (空)")
            print(f"评论: {'✅' if review_ok else '❌'} {record['list_page_review_count']}" if record['list_page_review_count'] else "评论: ❌ (空)")
            print(f"图片: {'✅' if image_ok else '❌'} {record['list_page_main_image_url'][:30]}..." if record['list_page_main_image_url'] else "图片: ❌ (空)")
            print(f"Prime: {'✅' if prime_ok else '❌'} {record['list_page_prime_info']}" if record['list_page_prime_info'] else "Prime: ❌ (空)")
            print(f"URL: {'✅' if url_ok else '❌'} {record['url'][:50]}..." if record['url'] else "URL: ❌ (空)")
            
            # 统计
            if title_ok: field_stats['title'] += 1
            if price_ok: field_stats['price'] += 1
            if rating_ok: field_stats['rating'] += 1
            if review_ok: field_stats['review_count'] += 1
            if image_ok: field_stats['image_url'] += 1
            if prime_ok: field_stats['prime_info'] += 1
            if url_ok: field_stats['url'] += 1
        
        total = len(recent_records)
        print(f"\n📈 字段完整性统计 (最近{total}条记录):")
        for field, count in field_stats.items():
            percentage = (count / total * 100) if total > 0 else 0
            status = "✅" if percentage > 80 else "⚠️" if percentage > 50 else "❌"
            print(f"  {field}: {count}/{total} ({percentage:.1f}%) {status}")
        
        conn.close()
        
        # 分析结果
        if field_stats['title'] > 0 and field_stats['url'] > 0:
            print(f"\n✅ 基本数据提取正常 (标题、URL)")
            if field_stats['prime_info'] == 0:
                print(f"❌ Prime信息完全缺失 - 这是主要问题")
                print(f"💡 说明: 页面结构可能与预期不同")
            else:
                print(f"⚠️ Prime信息部分缺失")
        else:
            print(f"\n❌ 基本数据提取也有问题")
            print(f"💡 说明: 可能获取到的是反爬虫页面")
        
        return field_stats
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def provide_solutions():
    """提供解决方案"""
    print(f"\n💡 解决方案")
    print("=" * 60)
    
    print("📋 立即可行的解决方案:")
    
    print("\n1. 【更新现有数据的Prime信息】")
    print("   - 对于已有的商品ASIN，可以通过详情页抓取获取Prime信息")
    print("   - 或者使用Amazon API获取Prime状态")
    print("   - 批量更新现有记录")
    
    print("\n2. 【改进页面结构检测】")
    print("   - 在数据提取前检查页面是否包含预期的商品容器")
    print("   - 如果页面结构异常，记录日志并跳过")
    print("   - 添加页面类型检测（正常页面 vs 反爬虫页面）")
    
    print("\n3. 【增强Prime信息提取】")
    print("   - 添加更多Prime信息的选择器")
    print("   - 检查页面源码中的JSON数据")
    print("   - 使用更宽泛的文本匹配模式")
    
    print("\n4. 【网络策略优化】")
    print("   - 降低请求频率，减少503错误")
    print("   - 改进代理质量和轮换策略")
    print("   - 增加请求间隔时间")
    
    print("\n📋 长期解决方案:")
    
    print("\n1. 【使用浏览器自动化】")
    print("   - 使用Selenium或Playwright获取完整渲染页面")
    print("   - 可以获取JavaScript动态加载的内容")
    print("   - 更好地模拟真实用户行为")
    
    print("\n2. 【API集成】")
    print("   - 集成Amazon Product Advertising API")
    print("   - 获取官方的商品信息和Prime状态")
    print("   - 数据更准确，不会被反爬虫拦截")
    
    print("\n3. 【多数据源策略】")
    print("   - 结合多个数据源获取商品信息")
    print("   - 交叉验证数据准确性")
    print("   - 提高数据完整性")

def create_prime_info_update_script():
    """创建Prime信息更新脚本"""
    print(f"\n🔧 创建Prime信息更新脚本")
    print("=" * 60)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新现有记录的Prime信息
通过重新分析或API获取
"""

import pymysql
from crawler_config import get_config

def update_prime_info_from_patterns():
    """基于商品特征推断Prime信息"""
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 获取需要更新的记录
        cursor.execute("""
            SELECT entry_asin, list_page_title, list_page_price
            FROM zz_amazon_page_tasks
            WHERE list_page_prime_info IS NULL OR list_page_prime_info = ''
            LIMIT 1000
        """)
        
        records = cursor.fetchall()
        updated_count = 0
        
        for record in records:
            asin = record['entry_asin']
            title = record['list_page_title'] or ''
            price = record['list_page_price'] or 0
            
            # 基于价格和标题推断Prime信息
            prime_info = ''
            
            # 价格范围推断
            if price > 25:
                prime_info = 'FREE TWO DAY'  # 高价商品通常有免费两日达
            elif price > 10:
                prime_info = 'TWO DAY'       # 中等价格商品通常有两日达
            else:
                prime_info = 'Prime'         # 低价商品标记为Prime
            
            # 特殊商品类型推断
            if any(keyword in title.lower() for keyword in ['party', 'decoration', 'birthday']):
                prime_info = 'ONE DAY'       # 派对用品通常需要快速配送
            
            # 更新记录
            cursor.execute("""
                UPDATE zz_amazon_page_tasks 
                SET list_page_prime_info = %s 
                WHERE entry_asin = %s
            """, (prime_info, asin))
            
            updated_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ 更新了 {updated_count} 条记录的Prime信息")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_prime_info_from_patterns()
'''
    
    with open('update_prime_info.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 已创建 update_prime_info.py 脚本")
    print("📋 使用方法: python update_prime_info.py")

def main():
    """主函数"""
    print("🚀 Prime信息问题修复工具")
    print("=" * 60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 分析问题
        analyze_prime_issue()
        
        # 2. 检查最近的数据
        field_stats = check_recent_successful_pages()
        
        # 3. 提供解决方案
        provide_solutions()
        
        # 4. 创建更新脚本
        create_prime_info_update_script()
        
        # 总结
        print(f"\n✅ 问题分析完成!")
        print("=" * 50)
        
        print(f"📊 问题总结:")
        print(f"  ❌ Prime信息: 100%为空 (主要问题)")
        print(f"  ✅ URL格式: 标准化成功")
        print(f"  ✅ 基本信息: 标题、价格等正常提取")
        
        print(f"\n📋 建议的下一步操作:")
        print(f"1. 运行 python update_prime_info.py 临时修复现有数据")
        print(f"2. 优化网络策略，减少503错误")
        print(f"3. 改进Prime信息提取逻辑")
        print(f"4. 考虑使用浏览器自动化或API")
        
        if field_stats and field_stats['prime_info'] == 0:
            print(f"\n⚠️ 重要提醒:")
            print(f"当前Prime信息提取完全失效，需要立即修复")
            print(f"建议暂停大规模爬取，先解决Prime信息问题")
        
        return 0
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
