#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新数据库表结构，添加新字段
"""

import pymysql

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'amazon_products',
    'charset': 'utf8mb4',
    'autocommit': True
}

def update_database():
    """更新数据库表结构"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功！")
        
        cursor = connection.cursor()
        
        # 检查字段是否已存在
        cursor.execute("DESCRIBE zz_amazon_products_spu")
        columns = [row[0] for row in cursor.fetchall()]
        
        print(f"当前表字段: {columns}")
        
        # 添加product_attributes字段
        if 'product_attributes' not in columns:
            print("添加 product_attributes 字段...")
            cursor.execute("""
                ALTER TABLE `zz_amazon_products_spu` 
                ADD COLUMN `product_attributes` text COLLATE utf8mb4_general_ci 
                COMMENT '产品参数属性 (JSON格式，包含技术规格等)' 
                AFTER `product_details`
            """)
            print("✅ product_attributes 字段添加成功")
        else:
            print("⚠️ product_attributes 字段已存在")
        
        # 添加temu_product_detail字段
        if 'temu_product_detail' not in columns:
            print("添加 temu_product_detail 字段...")
            cursor.execute("""
                ALTER TABLE `zz_amazon_products_spu` 
                ADD COLUMN `temu_product_detail` text COLLATE utf8mb4_general_ci 
                COMMENT 'Temu格式的产品详情 (JSON格式)' 
                AFTER `product_attributes`
            """)
            print("✅ temu_product_detail 字段添加成功")
        else:
            print("⚠️ temu_product_detail 字段已存在")
        
        # 再次检查表结构
        cursor.execute("DESCRIBE zz_amazon_products_spu")
        updated_columns = [row[0] for row in cursor.fetchall()]
        
        print(f"\n更新后的表字段: {updated_columns}")
        print(f"字段数量: {len(updated_columns)}")
        
        connection.close()
        print("✅ 数据库更新完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

if __name__ == "__main__":
    update_database()
