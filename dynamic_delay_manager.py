#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态延迟管理器
根据503错误率自动调整请求延迟
"""

import time
import random
import threading
from datetime import datetime, timedelta
from collections import deque
from dataclasses import dataclass
from typing import Tuple, Optional

@dataclass
class DelayConfig:
    """延迟配置"""
    min_delay: float
    max_delay: float
    
    def get_random_delay(self) -> float:
        """获取随机延迟"""
        return random.uniform(self.min_delay, self.max_delay)

class DynamicDelayManager:
    """动态延迟管理器"""
    
    def __init__(self):
        # 基础延迟配置
        self.base_delays = {
            'request': DelayConfig(8, 15),      # 基础请求延迟
            'page': DelayConfig(15, 30),        # 基础页面延迟
            'task': DelayConfig(20, 40),        # 基础任务延迟
            'error': DelayConfig(60, 120)       # 基础错误延迟
        }
        
        # 当前延迟配置（会动态调整）
        self.current_delays = {
            'request': DelayConfig(8, 15),
            'page': DelayConfig(15, 30),
            'task': DelayConfig(20, 40),
            'error': DelayConfig(60, 120)
        }
        
        # 错误统计
        self.error_history = deque(maxlen=100)  # 最近100个请求的错误记录
        self.request_history = deque(maxlen=100)  # 最近100个请求的时间记录
        
        # 动态调整参数
        self.error_rate_threshold = 0.1  # 10%错误率阈值
        self.adjustment_factor = 1.5      # 延迟调整倍数
        self.max_delay_multiplier = 5.0   # 最大延迟倍数
        self.min_delay_multiplier = 0.5   # 最小延迟倍数
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'total_503_errors': 0,
            'total_adjustments': 0,
            'current_multiplier': 1.0,
            'last_adjustment_time': None
        }
    
    def record_request(self, success: bool, error_type: Optional[str] = None):
        """记录请求结果"""
        with self.lock:
            now = datetime.now()
            
            # 记录请求
            self.request_history.append(now)
            self.stats['total_requests'] += 1
            
            # 记录错误
            if not success:
                self.error_history.append({
                    'time': now,
                    'type': error_type or 'unknown'
                })
                
                if error_type == '503':
                    self.stats['total_503_errors'] += 1
            
            # 检查是否需要调整延迟
            self._check_and_adjust_delays()
    
    def _check_and_adjust_delays(self):
        """检查并调整延迟"""
        # 计算最近的错误率
        recent_error_rate = self._calculate_recent_error_rate()
        recent_503_rate = self._calculate_recent_503_rate()
        
        # 决定是否需要调整
        should_increase = recent_503_rate > self.error_rate_threshold
        should_decrease = (recent_error_rate < 0.05 and 
                          self.stats['current_multiplier'] > 1.0 and
                          self._time_since_last_adjustment() > 300)  # 5分钟后才考虑降低
        
        if should_increase:
            self._increase_delays(recent_503_rate)
        elif should_decrease:
            self._decrease_delays()
    
    def _calculate_recent_error_rate(self, window_minutes: int = 10) -> float:
        """计算最近的错误率"""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=window_minutes)
        
        recent_errors = [e for e in self.error_history if e['time'] > cutoff_time]
        recent_requests = [r for r in self.request_history if r > cutoff_time]
        
        total_recent = len(recent_requests)
        if total_recent == 0:
            return 0.0
        
        return len(recent_errors) / total_recent
    
    def _calculate_recent_503_rate(self, window_minutes: int = 10) -> float:
        """计算最近的503错误率"""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=window_minutes)
        
        recent_503_errors = [e for e in self.error_history 
                           if e['time'] > cutoff_time and e['type'] == '503']
        recent_requests = [r for r in self.request_history if r > cutoff_time]
        
        total_recent = len(recent_requests)
        if total_recent == 0:
            return 0.0
        
        return len(recent_503_errors) / total_recent
    
    def _increase_delays(self, error_rate: float):
        """增加延迟"""
        # 根据错误率决定增加幅度
        if error_rate > 0.3:  # 30%以上错误率，大幅增加
            multiplier = self.adjustment_factor * 2
        elif error_rate > 0.2:  # 20%以上错误率，中等增加
            multiplier = self.adjustment_factor * 1.5
        else:  # 10-20%错误率，小幅增加
            multiplier = self.adjustment_factor
        
        new_multiplier = min(self.stats['current_multiplier'] * multiplier, 
                           self.max_delay_multiplier)
        
        if new_multiplier > self.stats['current_multiplier']:
            self._apply_delay_multiplier(new_multiplier)
            self.stats['total_adjustments'] += 1
            self.stats['last_adjustment_time'] = datetime.now()
            
            print(f"🔺 检测到503错误率 {error_rate:.1%}，增加延迟倍数至 {new_multiplier:.1f}x")
    
    def _decrease_delays(self):
        """减少延迟"""
        new_multiplier = max(self.stats['current_multiplier'] / self.adjustment_factor,
                           self.min_delay_multiplier)
        
        if new_multiplier < self.stats['current_multiplier']:
            self._apply_delay_multiplier(new_multiplier)
            self.stats['total_adjustments'] += 1
            self.stats['last_adjustment_time'] = datetime.now()
            
            print(f"🔻 错误率降低，减少延迟倍数至 {new_multiplier:.1f}x")
    
    def _apply_delay_multiplier(self, multiplier: float):
        """应用延迟倍数"""
        self.stats['current_multiplier'] = multiplier
        
        for delay_type, base_delay in self.base_delays.items():
            self.current_delays[delay_type] = DelayConfig(
                min_delay=base_delay.min_delay * multiplier,
                max_delay=base_delay.max_delay * multiplier
            )
    
    def _time_since_last_adjustment(self) -> float:
        """距离上次调整的时间（秒）"""
        if self.stats['last_adjustment_time'] is None:
            return float('inf')
        
        return (datetime.now() - self.stats['last_adjustment_time']).total_seconds()
    
    def get_delay(self, delay_type: str, page_number: int = 1, error_count: int = 0) -> float:
        """获取延迟时间"""
        with self.lock:
            if delay_type not in self.current_delays:
                delay_type = 'request'  # 默认使用请求延迟
            
            base_delay = self.current_delays[delay_type].get_random_delay()
            
            # 根据页数调整延迟（高页数增加延迟）
            if delay_type == 'page' and page_number > 10:
                page_multiplier = min(1 + (page_number - 10) * 0.1, 2.0)
                base_delay *= page_multiplier
            
            # 根据错误次数调整延迟
            if error_count > 0:
                error_multiplier = min(1 + error_count * 0.5, 3.0)
                base_delay *= error_multiplier
            
            return base_delay
    
    def get_503_delay(self, attempt_count: int) -> float:
        """获取503错误的特殊延迟"""
        # 503错误使用指数退避
        base_delay = self.current_delays['error'].min_delay
        delay = base_delay * (2 ** (attempt_count - 1))
        max_delay = self.current_delays['error'].max_delay * 2  # 503错误允许更长延迟
        
        return min(delay, max_delay)
    
    def get_current_config(self) -> dict:
        """获取当前延迟配置"""
        with self.lock:
            return {
                'current_multiplier': self.stats['current_multiplier'],
                'delays': {
                    delay_type: {
                        'min': config.min_delay,
                        'max': config.max_delay
                    }
                    for delay_type, config in self.current_delays.items()
                },
                'stats': self.stats.copy(),
                'recent_error_rate': self._calculate_recent_error_rate(),
                'recent_503_rate': self._calculate_recent_503_rate()
            }
    
    def reset_to_base_delays(self):
        """重置到基础延迟"""
        with self.lock:
            self.current_delays = {
                delay_type: DelayConfig(config.min_delay, config.max_delay)
                for delay_type, config in self.base_delays.items()
            }
            self.stats['current_multiplier'] = 1.0
            self.stats['total_adjustments'] += 1
            self.stats['last_adjustment_time'] = datetime.now()
            
            print("🔄 延迟配置已重置到基础值")
    
    def force_increase_delays(self, multiplier: float = 2.0):
        """强制增加延迟"""
        with self.lock:
            new_multiplier = min(self.stats['current_multiplier'] * multiplier,
                               self.max_delay_multiplier)
            self._apply_delay_multiplier(new_multiplier)
            self.stats['total_adjustments'] += 1
            self.stats['last_adjustment_time'] = datetime.now()
            
            print(f"🔺 手动增加延迟倍数至 {new_multiplier:.1f}x")
    
    def get_recommendations(self) -> list:
        """获取延迟优化建议"""
        recommendations = []
        
        recent_503_rate = self._calculate_recent_503_rate()
        recent_error_rate = self._calculate_recent_error_rate()
        current_multiplier = self.stats['current_multiplier']
        
        if recent_503_rate > 0.2:
            recommendations.append(f"503错误率过高 ({recent_503_rate:.1%})，建议进一步增加延迟")
        elif recent_503_rate > 0.1:
            recommendations.append(f"503错误率较高 ({recent_503_rate:.1%})，当前延迟倍数 {current_multiplier:.1f}x")
        elif recent_503_rate < 0.05 and current_multiplier > 1.5:
            recommendations.append(f"503错误率较低 ({recent_503_rate:.1%})，可考虑适当降低延迟")
        
        if self.stats['total_requests'] > 50:
            overall_503_rate = self.stats['total_503_errors'] / self.stats['total_requests']
            if overall_503_rate > 0.15:
                recommendations.append(f"整体503错误率过高 ({overall_503_rate:.1%})，建议检查代理质量")
        
        if not recommendations:
            recommendations.append("延迟配置合理，系统运行正常")
        
        return recommendations

# 全局动态延迟管理器实例
dynamic_delay_manager = DynamicDelayManager()

def get_smart_delay(delay_type: str, page_number: int = 1, error_count: int = 0) -> float:
    """获取智能延迟"""
    return dynamic_delay_manager.get_delay(delay_type, page_number, error_count)

def record_request_result(success: bool, error_type: Optional[str] = None):
    """记录请求结果"""
    dynamic_delay_manager.record_request(success, error_type)

def get_503_smart_delay(attempt_count: int) -> float:
    """获取503错误的智能延迟"""
    return dynamic_delay_manager.get_503_delay(attempt_count)

if __name__ == "__main__":
    # 测试动态延迟管理器
    manager = DynamicDelayManager()
    
    print("🧪 测试动态延迟管理器")
    print("=" * 50)
    
    # 模拟一些请求
    print("模拟正常请求...")
    for i in range(20):
        manager.record_request(True)
        time.sleep(0.1)
    
    print("模拟503错误...")
    for i in range(5):
        manager.record_request(False, '503')
        time.sleep(0.1)
    
    # 显示配置
    config = manager.get_current_config()
    print(f"\n当前延迟倍数: {config['current_multiplier']:.1f}x")
    print(f"页面延迟范围: {config['delays']['page']['min']:.1f}-{config['delays']['page']['max']:.1f}秒")
    print(f"最近503错误率: {config['recent_503_rate']:.1%}")
    
    # 显示建议
    recommendations = manager.get_recommendations()
    print("\n建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
