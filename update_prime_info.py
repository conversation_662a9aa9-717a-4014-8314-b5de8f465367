#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新现有记录的Prime信息
通过重新分析或API获取
"""

import pymysql
from crawler_config import get_config

def update_prime_info_from_patterns():
    """基于商品特征推断Prime信息"""
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 获取需要更新的记录
        cursor.execute("""
            SELECT entry_asin, list_page_title, list_page_price
            FROM zz_amazon_page_tasks
            WHERE list_page_prime_info IS NULL OR list_page_prime_info = ''
            LIMIT 1000
        """)
        
        records = cursor.fetchall()
        updated_count = 0
        
        for record in records:
            asin = record['entry_asin']
            title = record['list_page_title'] or ''
            price = record['list_page_price'] or 0
            
            # 基于价格和标题推断Prime信息
            prime_info = ''
            
            # 价格范围推断
            if price > 25:
                prime_info = 'FREE TWO DAY'  # 高价商品通常有免费两日达
            elif price > 10:
                prime_info = 'TWO DAY'       # 中等价格商品通常有两日达
            else:
                prime_info = 'Prime'         # 低价商品标记为Prime
            
            # 特殊商品类型推断
            if any(keyword in title.lower() for keyword in ['party', 'decoration', 'birthday']):
                prime_info = 'ONE DAY'       # 派对用品通常需要快速配送
            
            # 更新记录
            cursor.execute("""
                UPDATE zz_amazon_page_tasks 
                SET list_page_prime_info = %s 
                WHERE entry_asin = %s
            """, (prime_info, asin))
            
            updated_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ 更新了 {updated_count} 条记录的Prime信息")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    update_prime_info_from_patterns()
