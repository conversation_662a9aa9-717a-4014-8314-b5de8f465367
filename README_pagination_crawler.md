# Amazon分页爬虫系统使用说明

## 🎯 功能概述

这个系统实现了Amazon列表页的分页抓取，并将抓取到的商品自动插入到详情页任务队列中，支持完整的数据流转：

```
列表页URL → 分页抓取 → 商品数据 → 详情页任务队列 → 详情页抓取
```

## 📊 数据流向

1. **输入**: `zz_amazon_list_tasks` 表中的待抓取任务
2. **处理**: 分页抓取列表页商品
3. **输出**: `zz_amazon_page_tasks` 表中的详情页抓取任务

## 🚀 核心功能

### ✅ 分页抓取
- 自动识别Amazon分页结构
- 支持最大页数限制
- 断点续传功能
- 智能重试机制

### ✅ 商品数据提取
- ASIN、标题、价格、评分、评论数
- Prime配送信息检测
- 广告商品识别
- 商品图片和链接

### ✅ 任务管理
- 基于数据库的任务队列
- 状态跟踪（pending → in_progress → completed/failed）
- 进度监控
- 错误处理

### ✅ 重复处理
- 基于`entry_asin`的唯一键约束
- 自动跳过重复商品
- 详细的统计报告

## 📋 使用方法

### 1. 数据库准备

确保数据库中存在以下表：
- `zz_amazon_list_tasks` - 列表页抓取任务
- `zz_amazon_page_tasks` - 详情页抓取任务

### 2. 配置数据库连接

修改 `amazon_list_crawler_with_pagination.py` 中的数据库配置：

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'database': 'xace200_lsh',
    'charset': 'utf8mb4'
}
```

### 3. 创建抓取任务

```python
from amazon_list_crawler_with_pagination import create_list_task

# 创建任务
task_id = create_list_task(
    url="https://www.amazon.com/s?k=Kids+Party+Centerpieces&i=toys-and-games",
    category_id=12345,
    max_pages=5
)
```

### 4. 执行分页抓取

```python
from amazon_list_crawler_with_pagination import crawl_list_pages_with_pagination

# 执行抓取
success = crawl_list_pages_with_pagination(task_id)
```

### 5. 批量处理任务

```python
from amazon_list_crawler_with_pagination import process_pending_tasks

# 处理所有待处理任务
results = process_pending_tasks()
```

### 6. 查看任务状态

```python
from amazon_list_crawler_with_pagination import get_task_status

# 查看特定任务状态
tasks = get_task_status(task_id=task_id)

# 查看所有任务状态
all_tasks = get_task_status()
```

## 📊 数据字段映射

### 列表页数据 → zz_amazon_page_tasks

| 列表页字段 | 目标字段 | 说明 |
|-----------|---------|------|
| asin | entry_asin | 商品ASIN（唯一键） |
| product_url | url | 详情页URL |
| title | list_page_title | 商品标题（截取512字符） |
| price | list_page_price | 商品价格 |
| rating | list_page_rating | 商品评分 |
| review_count | list_page_review_count | 评论数量 |
| image_url | list_page_main_image_url | 主图URL |
| prime_info | list_page_prime_info | Prime配送信息 |
| is_sponsored | is_sponsored | 是否广告商品 |

## 🔧 配置选项

### 抓取参数
- `max_pages_to_crawl`: 最大抓取页数（默认5页）
- `retry_count`: 重试次数（默认3次）
- 页面间隔：1-3秒随机延迟

### 数据库配置
- 支持事务处理
- 自动重复检测
- 错误回滚机制

## 📈 性能特点

### 抓取效率
- 每页约16-24个商品
- 5页约100-120个商品
- 包含完整的商品信息

### 去重效果
- 基于ASIN的精确去重
- 重复商品自动跳过
- 实时统计报告

### 错误处理
- 网络错误自动重试
- 数据库错误回滚
- 详细的错误日志

## 🎯 实际效果

### 输入示例
```sql
INSERT INTO zz_amazon_list_tasks (url, source_category_id, max_pages_to_crawl) 
VALUES ('https://www.amazon.com/s?k=Kids+Party+Centerpieces', 12345, 5);
```

### 输出示例
```sql
-- 约100-120条记录插入到 zz_amazon_page_tasks
SELECT COUNT(*) FROM zz_amazon_page_tasks WHERE status='pending';
-- 结果: 约100-120条待处理的详情页任务
```

## 📋 文件说明

- `amazon_list_crawler_with_pagination.py` - 主要爬虫系统
- `test_pagination_crawler.py` - 分页功能测试
- `test_page_tasks_integration.py` - 集成测试
- `database_schema.sql` - 数据库表结构
- `README_pagination_crawler.md` - 使用说明

## 🚨 注意事项

1. **数据库连接**: 确保数据库配置正确
2. **表结构**: 确保相关表已创建
3. **网络环境**: 确保能访问Amazon网站
4. **反爬虫**: 系统已包含延迟和重试机制
5. **数据量**: 大量抓取时注意监控系统资源

## 🔄 下一步

1. 运行列表页分页抓取
2. 检查 `zz_amazon_page_tasks` 表中的任务
3. 运行详情页爬虫处理这些任务
4. 监控抓取进度和错误情况

## 📞 技术支持

如有问题，请检查：
1. 数据库连接配置
2. 表结构是否正确
3. 网络连接是否正常
4. 日志文件中的错误信息
