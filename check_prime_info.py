#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 list_page_prime_info 字段的实际情况
"""

import pymysql
import sys
from datetime import datetime
from crawler_config import get_config

def check_prime_info_in_database():
    """检查数据库中的Prime信息"""
    print("🔍 检查数据库中的Prime信息")
    print("=" * 60)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 检查总体情况
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN list_page_prime_info IS NULL OR list_page_prime_info = '' THEN 1 END) as empty_prime,
                COUNT(CASE WHEN list_page_prime_info IS NOT NULL AND list_page_prime_info != '' THEN 1 END) as with_prime
            FROM zz_amazon_page_tasks
        """)
        
        stats = cursor.fetchone()
        
        print(f"📊 Prime信息统计:")
        print(f"  总记录数: {stats['total']}")
        print(f"  Prime信息为空: {stats['empty_prime']}")
        print(f"  有Prime信息: {stats['with_prime']}")
        
        if stats['total'] > 0:
            empty_rate = (stats['empty_prime'] / stats['total']) * 100
            with_rate = (stats['with_prime'] / stats['total']) * 100
            print(f"  空值率: {empty_rate:.1f}%")
            print(f"  有值率: {with_rate:.1f}%")
        
        # 显示有Prime信息的记录示例
        if stats['with_prime'] > 0:
            cursor.execute("""
                SELECT entry_asin, list_page_title, list_page_prime_info, created_at
                FROM zz_amazon_page_tasks
                WHERE list_page_prime_info IS NOT NULL AND list_page_prime_info != ''
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            prime_examples = cursor.fetchall()
            
            print(f"\n📋 有Prime信息的记录示例 (最新10条):")
            for example in prime_examples:
                print(f"  ASIN: {example['entry_asin']}")
                print(f"    标题: {example['list_page_title'][:50]}...")
                print(f"    Prime: {example['list_page_prime_info']}")
                print(f"    时间: {example['created_at']}")
                print()
        
        # 显示最新的记录（无论是否有Prime信息）
        cursor.execute("""
            SELECT entry_asin, list_page_title, list_page_prime_info, url, created_at
            FROM zz_amazon_page_tasks
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        latest_records = cursor.fetchall()
        
        print(f"📋 最新5条记录:")
        for record in latest_records:
            prime_status = "✅" if record['list_page_prime_info'] else "❌"
            url_status = "✅" if record['url'] and 'amazon.com/dp/' in record['url'] else "❌"
            
            print(f"  ASIN: {record['entry_asin']}")
            print(f"    标题: {record['list_page_title'][:50]}...")
            print(f"    Prime: {prime_status} {record['list_page_prime_info'] or '(空)'}")
            print(f"    URL: {url_status} {record['url'][:60]}..." if record['url'] else f"    URL: {url_status} (空)")
            print(f"    时间: {record['created_at']}")
            print()
        
        # 检查最近抓取的数据
        cursor.execute("""
            SELECT 
                COUNT(*) as recent_total,
                COUNT(CASE WHEN list_page_prime_info IS NOT NULL AND list_page_prime_info != '' THEN 1 END) as recent_with_prime
            FROM zz_amazon_page_tasks
            WHERE created_at > NOW() - INTERVAL 1 DAY
        """)
        
        recent_stats = cursor.fetchone()
        
        if recent_stats['recent_total'] > 0:
            print(f"📈 最近24小时数据:")
            print(f"  新增记录: {recent_stats['recent_total']}")
            print(f"  有Prime信息: {recent_stats['recent_with_prime']}")
            recent_rate = (recent_stats['recent_with_prime'] / recent_stats['recent_total']) * 100
            print(f"  Prime信息率: {recent_rate:.1f}%")
        else:
            print(f"📈 最近24小时没有新增数据")
        
        conn.close()
        return stats
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_prime_extraction_with_real_data():
    """使用真实数据测试Prime信息提取"""
    print(f"\n🧪 测试Prime信息提取功能")
    print("=" * 60)
    
    try:
        from test_crawler import AmazonCrawler
        from bs4 import BeautifulSoup
        import os
        
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 测试HTML文件
        test_files = [
            ("doc/list.html", "list.html"),
            ("doc/list2.html", "list2.html")
        ]
        
        for file_path, file_name in test_files:
            if not os.path.exists(file_path):
                print(f"⚠️ 文件不存在: {file_path}")
                continue
            
            print(f"\n📋 测试 {file_name}:")
            
            # 加载HTML文件
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取商品
            products = crawler.extract_products_from_page(soup)
            
            if not products:
                print(f"❌ {file_name} 没有提取到商品")
                continue
            
            print(f"✅ {file_name} 提取到 {len(products)} 个商品")
            
            # 分析Prime信息
            prime_count = 0
            for product in products:
                if product.get('prime_info'):
                    prime_count += 1
            
            prime_rate = (prime_count / len(products)) * 100
            print(f"  Prime信息覆盖率: {prime_count}/{len(products)} ({prime_rate:.1f}%)")
            
            # 显示前3个商品的Prime信息
            print(f"  Prime信息示例:")
            for i, product in enumerate(products[:3], 1):
                asin = product.get('asin', 'N/A')
                prime_info = product.get('prime_info', 'N/A')
                prime_status = "✅" if prime_info and prime_info != 'N/A' else "❌"
                
                print(f"    商品{i} - ASIN: {asin}")
                print(f"      Prime: {prime_status} {prime_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_recent_crawl_logs():
    """检查最近的爬取日志"""
    print(f"\n📄 检查最近的爬取日志")
    print("=" * 60)
    
    try:
        import os
        
        log_file = "logs/amazon_crawler.log"
        
        if not os.path.exists(log_file):
            print(f"❌ 日志文件不存在: {log_file}")
            return False
        
        # 读取最后100行日志
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        recent_lines = lines[-100:] if len(lines) > 100 else lines
        
        # 查找Prime相关的日志
        prime_logs = []
        product_logs = []
        
        for line in recent_lines:
            if 'prime' in line.lower():
                prime_logs.append(line.strip())
            elif '个商品' in line or 'products' in line.lower():
                product_logs.append(line.strip())
        
        print(f"📋 最近的商品抓取日志:")
        for log in product_logs[-5:]:  # 显示最近5条
            print(f"  {log}")
        
        if prime_logs:
            print(f"\n📋 Prime相关日志:")
            for log in prime_logs[-5:]:  # 显示最近5条
                print(f"  {log}")
        else:
            print(f"\n⚠️ 没有找到Prime相关的日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Prime信息检查工具")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 检查数据库中的Prime信息
        db_stats = check_prime_info_in_database()
        
        # 2. 测试Prime信息提取功能
        extraction_success = test_prime_extraction_with_real_data()
        
        # 3. 检查最近的爬取日志
        log_check_success = check_recent_crawl_logs()
        
        # 总结
        print(f"\n✅ Prime信息检查完成!")
        print("=" * 50)
        
        if db_stats:
            if db_stats['with_prime'] == 0:
                print("❌ 问题确认: 数据库中所有记录的Prime信息都为空")
                print("💡 可能原因:")
                print("  1. 最近没有运行爬虫抓取新数据")
                print("  2. Prime信息提取逻辑有问题")
                print("  3. 页面结构发生变化")
                
                if extraction_success:
                    print("✅ 但是离线测试显示提取功能正常")
                    print("📋 建议:")
                    print("  1. 运行一次小规模爬取测试")
                    print("  2. 检查最新的Amazon页面结构")
                    print("  3. 更新现有数据的Prime信息")
                else:
                    print("❌ 离线测试也显示提取功能有问题")
                    print("📋 建议:")
                    print("  1. 检查HTML文件是否包含Prime信息")
                    print("  2. 更新Prime信息提取的选择器")
                    print("  3. 调试extract_single_product方法")
            else:
                print(f"✅ 数据库中有 {db_stats['with_prime']} 条记录包含Prime信息")
        
        return 0
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
