#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon 503错误修复脚本
快速修复当前遇到的503错误问题
"""

import sys
import pymysql
import time
from datetime import datetime
from crawler_config import get_config

def check_current_tasks():
    """检查当前任务状态"""
    print("🔍 检查当前任务状态")
    print("=" * 50)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查询进行中的任务
        cursor.execute("""
            SELECT id, url, source_category_id, status, max_pages_to_crawl, 
                   crawled_pages, error_message, updated_at
            FROM zz_amazon_list_tasks 
            WHERE status = 'in_progress'
            ORDER BY updated_at DESC
        """)
        
        in_progress_tasks = cursor.fetchall()
        
        print(f"📋 进行中的任务: {len(in_progress_tasks)}")
        
        for task in in_progress_tasks:
            print(f"  任务 {task['id']}:")
            print(f"    类目ID: {task['source_category_id']}")
            print(f"    进度: {task['crawled_pages']}/{task['max_pages_to_crawl']} 页")
            print(f"    最后更新: {task['updated_at']}")
            if task['error_message']:
                print(f"    错误信息: {task['error_message']}")
            print()
        
        # 查询失败的任务
        cursor.execute("""
            SELECT id, url, source_category_id, status, max_pages_to_crawl, 
                   crawled_pages, error_message, updated_at
            FROM zz_amazon_list_tasks 
            WHERE status = 'failed' AND updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY updated_at DESC
        """)
        
        failed_tasks = cursor.fetchall()
        
        print(f"❌ 最近1小时失败的任务: {len(failed_tasks)}")
        
        for task in failed_tasks:
            print(f"  任务 {task['id']}:")
            print(f"    类目ID: {task['source_category_id']}")
            print(f"    进度: {task['crawled_pages']}/{task['max_pages_to_crawl']} 页")
            print(f"    失败时间: {task['updated_at']}")
            print(f"    错误信息: {task['error_message']}")
            print()
        
        conn.close()
        return in_progress_tasks, failed_tasks
        
    except Exception as e:
        print(f"❌ 检查任务状态失败: {e}")
        return [], []

def fix_high_page_tasks():
    """修复高页数任务"""
    print("\n🔧 修复高页数任务")
    print("=" * 50)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查找高页数的进行中任务
        cursor.execute("""
            SELECT id, url, source_category_id, max_pages_to_crawl, crawled_pages
            FROM zz_amazon_list_tasks 
            WHERE status = 'in_progress' AND crawled_pages > 50
        """)
        
        high_page_tasks = cursor.fetchall()
        
        if not high_page_tasks:
            print("✅ 没有发现高页数任务")
            conn.close()
            return
        
        print(f"⚠️ 发现 {len(high_page_tasks)} 个高页数任务")
        
        for task in high_page_tasks:
            task_id = task['id']
            crawled_pages = task['crawled_pages']
            max_pages = task['max_pages_to_crawl']
            
            print(f"  任务 {task_id}: 已抓取 {crawled_pages} 页")
            
            # 选择修复策略
            if crawled_pages > 100:
                # 超过100页，标记为完成
                cursor.execute("""
                    UPDATE zz_amazon_list_tasks 
                    SET status = 'completed', 
                        error_message = '已抓取超过100页，自动标记完成',
                        updated_at = NOW()
                    WHERE id = %s
                """, (task_id,))
                print(f"    ✅ 标记为完成（超过100页）")
                
            elif crawled_pages > 50:
                # 50-100页，降低最大页数
                new_max_pages = min(crawled_pages + 10, 80)
                cursor.execute("""
                    UPDATE zz_amazon_list_tasks 
                    SET max_pages_to_crawl = %s,
                        error_message = '调整最大页数避免503错误',
                        updated_at = NOW()
                    WHERE id = %s
                """, (new_max_pages, task_id))
                print(f"    🔧 调整最大页数为 {new_max_pages}")
        
        conn.commit()
        conn.close()
        print("✅ 高页数任务修复完成")
        
    except Exception as e:
        print(f"❌ 修复高页数任务失败: {e}")

def reset_failed_tasks():
    """重置失败的任务"""
    print("\n🔄 重置失败的任务")
    print("=" * 50)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查找包含503错误的失败任务
        cursor.execute("""
            SELECT id, url, source_category_id, max_pages_to_crawl, crawled_pages, error_message
            FROM zz_amazon_list_tasks 
            WHERE status = 'failed' 
            AND (error_message LIKE '%503%' OR error_message LIKE '%Service Unavailable%')
            AND updated_at > DATE_SUB(NOW(), INTERVAL 2 HOUR)
        """)
        
        failed_503_tasks = cursor.fetchall()
        
        if not failed_503_tasks:
            print("✅ 没有发现503错误的失败任务")
            conn.close()
            return
        
        print(f"🔄 发现 {len(failed_503_tasks)} 个503错误的失败任务")
        
        for task in failed_503_tasks:
            task_id = task['id']
            crawled_pages = task['crawled_pages']
            max_pages = task['max_pages_to_crawl']
            
            print(f"  任务 {task_id}: 已抓取 {crawled_pages} 页")
            
            # 调整策略重置任务
            if crawled_pages >= 30:
                # 已抓取较多页面，降低最大页数
                new_max_pages = min(crawled_pages + 5, 40)
                cursor.execute("""
                    UPDATE zz_amazon_list_tasks 
                    SET status = 'pending',
                        max_pages_to_crawl = %s,
                        error_message = NULL,
                        updated_at = NOW()
                    WHERE id = %s
                """, (new_max_pages, task_id))
                print(f"    🔧 重置为pending，最大页数调整为 {new_max_pages}")
                
            else:
                # 抓取页面较少，直接重置
                cursor.execute("""
                    UPDATE zz_amazon_list_tasks 
                    SET status = 'pending',
                        error_message = NULL,
                        updated_at = NOW()
                    WHERE id = %s
                """, (task_id,))
                print(f"    🔄 重置为pending")
        
        conn.commit()
        conn.close()
        print("✅ 失败任务重置完成")
        
    except Exception as e:
        print(f"❌ 重置失败任务失败: {e}")

def optimize_pending_tasks():
    """优化待处理任务"""
    print("\n⚡ 优化待处理任务")
    print("=" * 50)
    
    try:
        config = get_config()
        conn = pymysql.connect(**config['db'])
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        
        # 查找高页数的待处理任务
        cursor.execute("""
            SELECT id, url, source_category_id, max_pages_to_crawl
            FROM zz_amazon_list_tasks 
            WHERE status = 'pending' AND max_pages_to_crawl > 50
            LIMIT 20
        """)
        
        high_page_pending = cursor.fetchall()
        
        if not high_page_pending:
            print("✅ 没有发现需要优化的待处理任务")
            conn.close()
            return
        
        print(f"⚡ 发现 {len(high_page_pending)} 个需要优化的待处理任务")
        
        for task in high_page_pending:
            task_id = task['id']
            max_pages = task['max_pages_to_crawl']
            
            # 限制最大页数为30，避免503错误
            new_max_pages = min(max_pages, 30)
            
            cursor.execute("""
                UPDATE zz_amazon_list_tasks 
                SET max_pages_to_crawl = %s,
                    updated_at = NOW()
                WHERE id = %s
            """, (new_max_pages, task_id))
            
            print(f"  任务 {task_id}: {max_pages} → {new_max_pages} 页")
        
        conn.commit()
        conn.close()
        print("✅ 待处理任务优化完成")
        
    except Exception as e:
        print(f"❌ 优化待处理任务失败: {e}")

def show_recommendations():
    """显示建议"""
    print("\n💡 建议和解决方案")
    print("=" * 50)
    
    print("🔧 立即修复措施:")
    print("  1. 限制最大页数为30页，避免深度抓取")
    print("  2. 增加页面间延迟到10-20秒")
    print("  3. 降低并发线程数到2-3个")
    print("  4. 启用更频繁的代理轮换")
    print()
    
    print("⚙️ 配置优化:")
    print("  1. 修改 crawler_config.py:")
    print("     - max_workers: 3")
    print("     - page_delay_range: (10, 20)")
    print("     - max_pages_per_task: 30")
    print()
    
    print("🚀 重启爬虫:")
    print("  1. 停止当前爬虫进程")
    print("  2. 运行: python run_crawler.py --workers 3")
    print("  3. 监控日志: tail -f logs/amazon_crawler.log")
    print()
    
    print("📊 监控指标:")
    print("  1. 503错误率应低于5%")
    print("  2. 页面抓取成功率应高于90%")
    print("  3. 平均响应时间应低于10秒")

def main():
    """主函数"""
    print("🚀 Amazon 503错误修复工具")
    print("=" * 60)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 检查当前任务状态
        in_progress, failed = check_current_tasks()
        
        # 2. 修复高页数任务
        fix_high_page_tasks()
        
        # 3. 重置失败的任务
        reset_failed_tasks()
        
        # 4. 优化待处理任务
        optimize_pending_tasks()
        
        # 5. 显示建议
        show_recommendations()
        
        print("\n✅ 503错误修复完成!")
        print("\n📋 下一步操作:")
        print("1. 重启爬虫: python run_crawler.py --workers 3")
        print("2. 监控日志: tail -f logs/amazon_crawler.log")
        print("3. 检查错误率是否降低")
        
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
