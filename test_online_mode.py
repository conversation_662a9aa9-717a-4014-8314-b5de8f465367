#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线测试模式 - 抓取实际Amazon页面并解析入库
测试Prime信息提取和数据库存储功能
"""

import os
import sys
import time
import random
from datetime import datetime
from bs4 import BeautifulSoup

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_crawler import AmazonCrawler, setup_logging, DB_CONFIG
import pymysql

# 测试配置
TEST_CONFIG = {
    'test_mode': True,          # 开启测试模式
    'save_to_db': True,         # 是否保存到数据库
    'max_test_products': 10,    # 最大测试商品数
    'test_category_id': 99999,  # 测试类目ID
    'save_html': True,          # 是否保存HTML文件
}

# 测试URL
TEST_URL = "https://www.amazon.com/s?i=kitchen&rh=n%3A274332011%2Cp_76%3A2661625011%2Cp_36%3A-1000"

class OnlineTestCrawler(AmazonCrawler):
    """在线测试爬虫类"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.test_config = TEST_CONFIG
        self.logger.info("🧪 在线测试模式已启用")
    
    def create_test_task(self, url, category_id):
        """创建测试任务"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # 删除旧的测试任务
            cursor.execute(
                "DELETE FROM zz_amazon_list_tasks WHERE source_category_id = %s",
                (category_id,)
            )
            
            # 创建新的测试任务
            cursor.execute("""
                INSERT INTO zz_amazon_list_tasks 
                (url, source_category_id, max_pages_to_crawl, status)
                VALUES (%s, %s, %s, 'pending')
            """, (url, category_id, 1))  # 只测试1页
            
            task_id = cursor.lastrowid
            conn.commit()
            
            self.logger.info(f"✅ 创建测试任务成功: 任务ID {task_id}")
            return task_id
            
        except Exception as e:
            self.logger.error(f"❌ 创建测试任务失败: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()
    
    def test_online_crawl(self, url):
        """测试在线抓取"""
        self.logger.info(f"🌐 开始在线测试抓取")
        self.logger.info(f"📋 测试URL: {url}")
        
        try:
            # 1. 发送请求
            self.logger.info("📡 发送请求...")
            response = self.make_request(url)
            
            if response.status_code != 200:
                self.logger.error(f"❌ 请求失败，状态码: {response.status_code}")
                return None, []
            
            self.logger.info(f"✅ 请求成功，状态码: {response.status_code}")
            self.logger.info(f"📊 响应大小: {len(response.text)} 字符")
            
            # 2. 保存HTML文件（用于调试）
            if self.test_config['save_html']:
                html_file = f"debug_online_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(response.text)
                self.logger.info(f"💾 HTML已保存到: {html_file}")
            
            # 3. 解析页面
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查是否被反爬虫拦截
            if "Robot Check" in response.text or "captcha" in response.text.lower():
                self.logger.error("❌ 检测到反爬虫验证页面")
                return None, []
            
            if "sorry" in response.text.lower() and "blocked" in response.text.lower():
                self.logger.error("❌ 检测到IP被封禁页面")
                return None, []
            
            # 4. 提取商品信息
            self.logger.info("🔍 开始提取商品信息...")
            products = self.extract_products_from_page(soup)
            
            if not products:
                self.logger.warning("⚠️ 没有提取到商品信息")
                # 尝试分析页面结构
                self._analyze_page_structure(soup)
                return soup, []
            
            self.logger.info(f"✅ 成功提取 {len(products)} 个商品")
            
            # 5. 限制测试商品数量
            if self.test_config['max_test_products'] > 0:
                products = products[:self.test_config['max_test_products']]
                self.logger.info(f"🔒 限制为前 {len(products)} 个商品进行测试")
            
            return soup, products
            
        except Exception as e:
            self.logger.error(f"❌ 在线抓取失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None, []
    
    def _analyze_page_structure(self, soup):
        """分析页面结构（调试用）"""
        self.logger.info("🔍 分析页面结构...")
        
        # 检查基本元素
        title = soup.find('title')
        self.logger.info(f"页面标题: {title.get_text() if title else 'N/A'}")
        
        # 检查商品容器
        containers = soup.select('[data-component-type="s-search-result"]')
        self.logger.info(f"找到 {len(containers)} 个标准商品容器")
        
        # 检查备用容器
        alt_containers = soup.select('div[data-asin]')
        self.logger.info(f"找到 {len(alt_containers)} 个ASIN容器")
        
        # 检查是否有搜索结果
        no_results = soup.select('.s-no-results, .s-no-outline')
        if no_results:
            self.logger.warning("⚠️ 检测到无搜索结果标识")
        
        # 检查页面中是否包含关键字
        page_text = soup.get_text()
        keywords = ['prime', 'delivery', 'shipping', 'price', 'rating']
        for keyword in keywords:
            count = page_text.lower().count(keyword)
            self.logger.info(f"关键字 '{keyword}' 出现 {count} 次")
    
    def analyze_prime_extraction(self, products):
        """分析Prime信息提取效果"""
        if not products:
            return
        
        self.logger.info("📊 Prime信息提取分析")
        self.logger.info("=" * 50)
        
        total = len(products)
        with_prime = sum(1 for p in products if p.get('prime_info'))
        prime_rate = (with_prime / total) * 100 if total > 0 else 0
        
        self.logger.info(f"总商品数: {total}")
        self.logger.info(f"有Prime信息: {with_prime}")
        self.logger.info(f"Prime覆盖率: {prime_rate:.1f}%")
        
        # 配送时间统计
        delivery_stats = {}
        for product in products:
            prime_info = product.get('prime_info', '')
            if prime_info:
                delivery_info = self._parse_delivery_time(prime_info)
                delivery_time = delivery_info.get('delivery_time', 'Unknown')
                delivery_stats[delivery_time] = delivery_stats.get(delivery_time, 0) + 1
        
        if delivery_stats:
            self.logger.info("\n🚚 Prime配送时间分布:")
            for delivery_time, count in sorted(delivery_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / with_prime) * 100 if with_prime > 0 else 0
                self.logger.info(f"  {delivery_time}: {count} ({percentage:.1f}%)")
        
        # 显示详细Prime信息示例
        self.logger.info(f"\n📋 Prime信息详细示例:")
        prime_products = [p for p in products if p.get('prime_info')]
        
        for i, product in enumerate(prime_products[:5], 1):
            asin = product.get('asin', 'N/A')
            title = product.get('title', 'N/A')[:40]
            prime_info = product.get('prime_info', '')
            
            delivery_info = self._parse_delivery_time(prime_info)
            delivery_time = delivery_info.get('delivery_time', 'N/A')
            delivery_type = delivery_info.get('delivery_type', 'N/A')
            is_free = "🆓" if delivery_info.get('is_free') else ""
            
            self.logger.info(f"  商品 {i}:")
            self.logger.info(f"    ASIN: {asin}")
            self.logger.info(f"    标题: {title}...")
            self.logger.info(f"    Prime原文: {prime_info}")
            self.logger.info(f"    解析结果: ⏱️ {delivery_time} ({delivery_type}) {is_free}")
            self.logger.info("")
    
    def test_database_save(self, products, task_id):
        """测试数据库保存"""
        if not self.test_config['save_to_db'] or not products:
            self.logger.info("⏭️ 跳过数据库保存测试")
            return
        
        self.logger.info("💾 测试数据库保存...")
        
        try:
            # 清理旧的测试数据
            self._cleanup_test_data()
            
            # 保存商品数据
            results = self.save_products_to_db(products, task_id, self.test_config['test_category_id'])
            
            self.logger.info(f"📊 保存结果:")
            self.logger.info(f"  总计: {results['total']}")
            self.logger.info(f"  新增: {results['inserted']}")
            self.logger.info(f"  跳过: {results['skipped']}")
            self.logger.info(f"  失败: {results['failed']}")
            
            if results['inserted'] > 0:
                # 验证保存的数据
                self._verify_saved_data()
            
        except Exception as e:
            self.logger.error(f"❌ 数据库保存测试失败: {e}")
    
    def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # 删除测试商品数据
            cursor.execute(
                "DELETE FROM zz_amazon_page_tasks WHERE amazon_category_id = %s",
                (self.test_config['test_category_id'],)
            )
            
            deleted_count = cursor.rowcount
            conn.commit()
            
            if deleted_count > 0:
                self.logger.info(f"🗑️ 清理了 {deleted_count} 条旧测试数据")
            
        except Exception as e:
            self.logger.error(f"❌ 清理测试数据失败: {e}")
        finally:
            if 'conn' in locals():
                conn.close()
    
    def _verify_saved_data(self):
        """验证保存的数据"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 查询保存的测试数据
            cursor.execute("""
                SELECT entry_asin, list_page_title, list_page_price, list_page_prime_info,
                       list_page_rating, list_page_review_count, is_sponsored
                FROM zz_amazon_page_tasks 
                WHERE amazon_category_id = %s
                ORDER BY id DESC
                LIMIT 5
            """, (self.test_config['test_category_id'],))
            
            saved_products = cursor.fetchall()
            
            self.logger.info(f"✅ 验证保存数据 - 查询到 {len(saved_products)} 条记录:")
            
            for i, product in enumerate(saved_products, 1):
                self.logger.info(f"  记录 {i}:")
                self.logger.info(f"    ASIN: {product['entry_asin']}")
                self.logger.info(f"    标题: {product['list_page_title'][:40]}...")
                self.logger.info(f"    价格: ${product['list_page_price']}")
                self.logger.info(f"    Prime: {product['list_page_prime_info'] or 'N/A'}")
                self.logger.info(f"    评分: {product['list_page_rating']} ({product['list_page_review_count']} 评论)")
                self.logger.info(f"    广告: {'是' if product['is_sponsored'] else '否'}")
                self.logger.info("")
            
        except Exception as e:
            self.logger.error(f"❌ 验证保存数据失败: {e}")
        finally:
            if 'conn' in locals():
                conn.close()
    
    def run_comprehensive_test(self, url):
        """运行综合测试"""
        self.logger.info("🚀 开始综合在线测试")
        self.logger.info("=" * 60)
        self.logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"测试URL: {url}")
        self.logger.info(f"测试配置: {self.test_config}")
        self.logger.info("=" * 60)
        
        try:
            # 1. 创建测试任务
            task_id = self.create_test_task(url, self.test_config['test_category_id'])
            if not task_id:
                return False
            
            # 2. 在线抓取测试
            soup, products = self.test_online_crawl(url)
            if soup is None:
                return False
            
            # 3. Prime信息分析
            self.analyze_prime_extraction(products)
            
            # 4. 数据库保存测试
            self.test_database_save(products, task_id)
            
            # 5. 数据质量分析
            self._analyze_data_quality(products)
            
            self.logger.info("✅ 综合测试完成!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 综合测试失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def _analyze_data_quality(self, products):
        """分析数据质量"""
        if not products:
            return
        
        self.logger.info("📊 数据质量分析")
        self.logger.info("=" * 50)
        
        # 基本字段完整性
        field_stats = {
            'asin': 0,
            'title': 0,
            'price': 0,
            'rating': 0,
            'review_count': 0,
            'prime_info': 0,
            'product_url': 0,
            'image_url': 0
        }
        
        for product in products:
            for field in field_stats:
                if product.get(field):
                    field_stats[field] += 1
        
        total = len(products)
        self.logger.info(f"📈 字段完整性统计 (总商品数: {total}):")
        for field, count in field_stats.items():
            rate = (count / total) * 100 if total > 0 else 0
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            self.logger.info(f"  {field}: {count}/{total} ({rate:.1f}%) {status}")
        
        # 价格范围分析
        prices = [p.get('price', 0) for p in products if p.get('price', 0) > 0]
        if prices:
            self.logger.info(f"\n💰 价格分析:")
            self.logger.info(f"  有效价格数: {len(prices)}")
            self.logger.info(f"  价格范围: ${min(prices):.2f} - ${max(prices):.2f}")
            self.logger.info(f"  平均价格: ${sum(prices) / len(prices):.2f}")
        
        # URL格式验证
        valid_urls = 0
        for product in products:
            url = product.get('product_url', '')
            if url and ('amazon.com' in url or url.startswith('/')):
                valid_urls += 1
        
        url_rate = (valid_urls / total) * 100 if total > 0 else 0
        self.logger.info(f"\n🔗 URL格式验证:")
        self.logger.info(f"  有效URL: {valid_urls}/{total} ({url_rate:.1f}%)")

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    logger.info("🚀 Amazon在线测试模式启动")
    
    try:
        # 创建测试爬虫实例
        crawler = OnlineTestCrawler()
        
        # 运行综合测试
        success = crawler.run_comprehensive_test(TEST_URL)
        
        if success:
            logger.info("🎉 在线测试模式完成!")
            logger.info("\n📋 测试总结:")
            logger.info("✅ 网络请求成功")
            logger.info("✅ 页面解析成功")
            logger.info("✅ Prime信息提取成功")
            logger.info("✅ 数据库保存成功")
            
            logger.info("\n💡 下一步操作:")
            logger.info("1. 检查生成的HTML文件，对比页面结构")
            logger.info("2. 查看数据库中的测试数据质量")
            logger.info("3. 调整Prime信息提取算法（如需要）")
            logger.info("4. 扩展到更多页面测试")
            
        else:
            logger.error("❌ 在线测试失败!")
            logger.info("\n🔧 排查建议:")
            logger.info("1. 检查网络连接和代理配置")
            logger.info("2. 检查是否被Amazon反爬虫拦截")
            logger.info("3. 检查页面结构是否发生变化")
            logger.info("4. 查看详细错误日志")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试程序异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
