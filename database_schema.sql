-- Amazon分页爬虫数据库表结构
-- 创建时间: 2025-06-14

-- 1. 任务队列表（您已提供的结构）
CREATE TABLE `zz_amazon_list_tasks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待抓取的列表页(搜索页)URL',
  `source_category_id` bigint NOT NULL COMMENT '来源的zz_amazon_category表中的category_id',
  `status` enum('pending','in_progress','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `max_pages_to_crawl` tinyint NOT NULL DEFAULT '5' COMMENT '为此URL最多抓取的页数',
  `crawled_pages` tinyint NOT NULL DEFAULT '0' COMMENT '已抓取的页数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_category_id` (`source_category_id`) COMMENT '一个类目通常只需要一个任务'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='亚马逊列表页爬虫任务队列';

-- 2. 商品基础信息表（用于存储从列表页抓取的商品）
CREATE TABLE `zz_amazon_products` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Amazon商品唯一标识',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品标题',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品主图URL',
  `product_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品详情页URL',
  `rating` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '商品评分(0-5)',
  `review_count` int NOT NULL DEFAULT '0' COMMENT '评论数量',
  `category_id` bigint NOT NULL COMMENT '所属类目ID',
  `is_detail_crawled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已抓取详情页(0:否, 1:是)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asin` (`asin`) COMMENT 'ASIN唯一索引',
  KEY `idx_category_id` (`category_id`) COMMENT '类目索引',
  KEY `idx_is_detail_crawled` (`is_detail_crawled`) COMMENT '详情抓取状态索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon商品基础信息表';

-- 3. 类目表（如果不存在的话）
CREATE TABLE IF NOT EXISTS `zz_amazon_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类目名称',
  `parent_category_id` bigint DEFAULT NULL COMMENT '父类目ID',
  `category_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '类目页面URL',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '类目层级',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活(0:否, 1:是)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_category_id` (`parent_category_id`) COMMENT '父类目索引',
  KEY `idx_level` (`level`) COMMENT '层级索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon类目表';

-- 4. 商品详情表（用于存储详情页抓取的数据）
CREATE TABLE `zz_amazon_product_details` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `asin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Amazon商品唯一标识',
  `description` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '商品描述',
  `features` json COMMENT '商品特性列表',
  `specifications` json COMMENT '商品规格参数',
  `images` json COMMENT '商品图片列表',
  `variants` json COMMENT '商品变体信息',
  `stock_status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Unknown' COMMENT '库存状态',
  `stock_quantity` int DEFAULT NULL COMMENT '库存数量',
  `brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '品牌',
  `manufacturer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '制造商',
  `dimensions` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品尺寸',
  `weight` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品重量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asin` (`asin`) COMMENT 'ASIN唯一索引',
  CONSTRAINT `fk_product_details_asin` FOREIGN KEY (`asin`) REFERENCES `zz_amazon_products` (`asin`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon商品详情表';

-- 5. 商品变体SKU表
CREATE TABLE `zz_amazon_product_skus` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `parent_asin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父商品ASIN',
  `sku_asin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SKU的ASIN',
  `variation_attributes` json COMMENT '变体属性(颜色、尺寸等)',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'SKU价格',
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SKU图片URL',
  `stock_status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Unknown' COMMENT '库存状态',
  `stock_quantity` int DEFAULT NULL COMMENT '库存数量',
  `is_available` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可购买',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_asin` (`sku_asin`) COMMENT 'SKU ASIN唯一索引',
  KEY `idx_parent_asin` (`parent_asin`) COMMENT '父商品索引',
  CONSTRAINT `fk_sku_parent_asin` FOREIGN KEY (`parent_asin`) REFERENCES `zz_amazon_products` (`asin`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Amazon商品SKU表';

-- 6. 抓取日志表
CREATE TABLE `zz_amazon_crawl_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` int DEFAULT NULL COMMENT '关联的任务ID',
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '抓取的URL',
  `page_number` int NOT NULL DEFAULT '1' COMMENT '页码',
  `products_found` int NOT NULL DEFAULT '0' COMMENT '发现的商品数量',
  `products_saved` int NOT NULL DEFAULT '0' COMMENT '保存的商品数量',
  `status` enum('success','failed','partial') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'success',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误信息',
  `response_time` int DEFAULT NULL COMMENT '响应时间(毫秒)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`) COMMENT '任务ID索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
  CONSTRAINT `fk_crawl_log_task_id` FOREIGN KEY (`task_id`) REFERENCES `zz_amazon_list_tasks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='抓取日志表';

-- 插入示例类目数据
INSERT INTO `zz_amazon_category` (`category_id`, `category_name`, `category_url`, `level`) VALUES
(12345, 'Kids Party Centerpieces', 'https://www.amazon.com/s?k=Kids%27+Party+Centerpieces&i=toys-and-games', 1),
(12346, 'Toys & Games', 'https://www.amazon.com/toys-and-games/b?node=165793011', 1),
(12347, 'Home & Kitchen', 'https://www.amazon.com/home-kitchen/b?node=1055398', 1);

-- 创建索引优化查询性能
CREATE INDEX idx_products_category_price ON zz_amazon_products(category_id, price);
CREATE INDEX idx_products_rating_reviews ON zz_amazon_products(rating, review_count);
CREATE INDEX idx_tasks_status_updated ON zz_amazon_list_tasks(status, updated_at);
