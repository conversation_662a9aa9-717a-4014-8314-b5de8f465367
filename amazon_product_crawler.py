import requests
import time
import random
from extract_amazon_products import extract_product_info, save_products_to_db, HEADERS
from bs4 import BeautifulSoup

def crawl_amazon_search_results(keyword, max_pages=3):
    """抓取Amazon搜索结果"""
    base_url = "https://www.amazon.com/s"
    all_products = []
    
    for page in range(1, max_pages + 1):
        params = {
            'k': keyword,
            'page': page,
            'ref': 'sr_pg_' + str(page)
        }
        
        try:
            print(f"抓取第 {page} 页...")
            
            # 添加随机延时
            time.sleep(random.uniform(2, 5))
            
            response = requests.get(base_url, params=params, headers=HEADERS)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            products = extract_product_info(soup, "https://www.amazon.com")
            
            all_products.extend(products)
            print(f"第 {page} 页提取到 {len(products)} 个产品")
            
        except Exception as e:
            print(f"抓取第 {page} 页失败: {e}")
            break
    
    return all_products

if __name__ == "__main__":
    # 示例：抓取珠宝制作相关产品
    keyword = "jewelry making bead looms"
    products = crawl_amazon_search_results(keyword, max_pages=2)
    
    if products:
        save_products_to_db(products)
        print(f"总共保存了 {len(products)} 个产品")
