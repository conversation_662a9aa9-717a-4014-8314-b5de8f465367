#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线测试模式 - 处理本地HTML文件并提取商品信息
专门用于处理 doc/list3.html 和其他本地HTML文件
"""

import sys
import os
import re
from datetime import datetime
from bs4 import BeautifulSoup
from decimal import Decimal
from urllib.parse import urljoin

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler_config import get_config

class OfflineAmazonParser:
    """离线Amazon页面解析器"""
    
    def __init__(self, config=None):
        self.config = config or get_config()
        self.base_url = 'https://www.amazon.com'
        
        # 商品选择器
        self.selectors = {
            'product_container': '[data-component-type="s-search-result"]',
            'product_title': 'h2 a span, [data-cy="title-recipe"] h2 span, h2 span',
            'product_price': '.a-price .a-offscreen, .a-price-whole',
            'product_rating': '.a-icon-alt',
            'product_reviews': 'a[aria-label*="ratings"], .a-size-base',
            'product_image': 'img.s-image, img[data-image-latency]',
            'product_link': 'h2 a, [data-cy="title-recipe"] a',
            'prime_badge': '[aria-label*="Prime"], .a-icon-prime, i[aria-label*="Prime"]',
            'sponsored_label': '.s-sponsored-label, [aria-label*="Sponsored"]',
            'original_price': '.a-text-price .a-offscreen, [data-a-strike="true"] .a-offscreen'
        }
    
    def load_html_file(self, file_path):
        """
        加载HTML文件
        
        Args:
            file_path: HTML文件路径
            
        Returns:
            BeautifulSoup: 解析后的HTML对象
        """
        try:
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            print(f"✅ 成功加载文件: {file_path}")
            print(f"📊 文件大小: {len(html_content)} 字符")
            
            return soup
            
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None
    
    def extract_products_from_page(self, soup):
        """
        从页面提取商品信息
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            list: 商品信息列表
        """
        products = []
        
        try:
            # 查找商品容器
            containers = soup.select(self.selectors['product_container'])
            print(f"🔍 找到 {len(containers)} 个商品容器")
            
            for i, container in enumerate(containers, 1):
                try:
                    product = self.extract_single_product(container)
                    if product:
                        product['list_position'] = i
                        products.append(product)
                        
                except Exception as e:
                    print(f"⚠️ 提取第 {i} 个商品失败: {e}")
                    continue
            
            print(f"✅ 成功提取 {len(products)} 个商品")
            return products
            
        except Exception as e:
            print(f"❌ 提取商品失败: {e}")
            return []
    
    def extract_single_product(self, container):
        """
        提取单个商品信息
        
        Args:
            container: 商品容器元素
            
        Returns:
            dict: 商品信息
        """
        try:
            product = {}
            
            # 提取ASIN
            asin = container.get('data-asin', '')
            if not asin:
                return None
            product['asin'] = asin
            
            # 提取标题
            title = self._extract_title(container)
            product['title'] = title
            
            # 提取价格信息
            price_info = self._extract_price_info(container)
            product.update(price_info)
            
            # 提取图片URL
            product['image_url'] = self._extract_image_url(container)
            
            # 提取商品链接
            product['product_url'] = self._extract_product_url(container)
            
            # 提取评分和评论数
            rating_info = self._extract_rating_info(container)
            product.update(rating_info)
            
            # 提取Prime信息 - 重点优化
            product['prime_info'] = self._extract_prime_info(container)
            
            # 检测是否为广告商品
            product['is_sponsored'] = self._detect_sponsored(container)
            
            # 提取购买数量信息
            product['bought_num'] = self._extract_bought_info(container)
            
            # 添加时间戳
            product['extracted_at'] = datetime.now().isoformat()
            
            return product
            
        except Exception as e:
            print(f"❌ 提取商品信息失败: {e}")
            return None
    
    def _extract_title(self, container):
        """提取商品标题"""
        try:
            for selector in self.selectors['product_title'].split(', '):
                title_elem = container.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    if title:
                        return title[:512]  # 限制长度
            return ''
        except:
            return ''
    
    def _extract_price_info(self, container):
        """提取价格信息"""
        price_info = {
            'current_price': Decimal('0.00'),
            'original_price': Decimal('0.00'),
            'discount_percent': 0
        }
        
        try:
            # 提取当前价格
            for selector in self.selectors['product_price'].split(', '):
                price_elem = container.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', '').replace('$', ''))
                    if price_match:
                        try:
                            price_info['current_price'] = Decimal(price_match.group())
                            break
                        except:
                            continue
            
            # 提取原价
            for selector in self.selectors['original_price'].split(', '):
                orig_elem = container.select_one(selector)
                if orig_elem:
                    orig_text = orig_elem.get_text(strip=True)
                    orig_match = re.search(r'[\d,]+\.?\d*', orig_text.replace(',', '').replace('$', ''))
                    if orig_match:
                        try:
                            price_info['original_price'] = Decimal(orig_match.group())
                            break
                        except:
                            continue
            
            # 计算折扣比例
            if price_info['original_price'] > 0 and price_info['current_price'] > 0:
                discount = (price_info['original_price'] - price_info['current_price']) / price_info['original_price'] * 100
                price_info['discount_percent'] = round(float(discount), 1)
            
        except Exception as e:
            print(f"⚠️ 提取价格信息失败: {e}")
        
        return price_info
    
    def _extract_image_url(self, container):
        """提取商品图片URL"""
        try:
            for selector in self.selectors['product_image'].split(', '):
                img_elem = container.select_one(selector)
                if img_elem:
                    return img_elem.get('src', '') or img_elem.get('data-src', '')
            return ''
        except:
            return ''
    
    def _extract_product_url(self, container):
        """提取商品链接"""
        try:
            for selector in self.selectors['product_link'].split(', '):
                link_elem = container.select_one(selector)
                if link_elem and link_elem.get('href'):
                    href = link_elem['href']
                    return urljoin(self.base_url, href)
            return ''
        except:
            return ''
    
    def _extract_rating_info(self, container):
        """提取评分和评论数信息"""
        rating_info = {
            'rating': Decimal('0.0'),
            'review_count': 0
        }
        
        try:
            # 提取评分
            rating_elem = container.select_one(self.selectors['product_rating'])
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        rating_info['rating'] = Decimal(rating_match.group(1))
                    except:
                        pass
            
            # 提取评论数
            review_elems = container.select(self.selectors['product_reviews'])
            for elem in review_elems:
                if elem:
                    review_text = elem.get('aria-label', '') or elem.get_text(strip=True)
                    # 匹配各种评论数格式
                    review_patterns = [
                        r'(\d+)\s*ratings?',
                        r'\((\d+)\)',
                        r'(\d+)\s*reviews?'
                    ]
                    
                    for pattern in review_patterns:
                        review_match = re.search(pattern, review_text.replace(',', ''), re.IGNORECASE)
                        if review_match:
                            try:
                                rating_info['review_count'] = int(review_match.group(1))
                                break
                            except:
                                continue
                    
                    if rating_info['review_count'] > 0:
                        break
        
        except Exception as e:
            print(f"⚠️ 提取评分信息失败: {e}")
        
        return rating_info
    
    def _extract_prime_info(self, container):
        """
        提取Prime配送信息 - 增强版，包含配送时间解析
        
        Args:
            container: 商品容器元素
            
        Returns:
            str: Prime配送信息，如果没有返回空字符串
        """
        try:
            # 方法1: 通过Prime相关的选择器查找
            prime_selectors = [
                'i[aria-label*="Prime"]',
                '[aria-label*="Prime"]',
                '.a-icon-prime',
                'span[aria-label*="Prime"]',
                '.s-prime',
                '.a-color-base[aria-label*="Prime"]'
            ]
            
            for selector in prime_selectors:
                prime_elem = container.select_one(selector)
                if prime_elem:
                    prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                    if prime_text and 'prime' in prime_text.lower():
                        # 解析并打印配送时间
                        delivery_info = self._parse_delivery_time(prime_text)
                        if delivery_info['delivery_time']:
                            print(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                        return prime_text[:255]  # 限制长度
            
            # 方法2: 在容器文本中搜索Prime相关信息
            container_text = container.get_text()
            prime_patterns = [
                r'(Prime\s+.*?delivery.*?)(?:\n|$)',
                r'(FREE\s+.*?Prime.*?delivery.*?)(?:\n|$)',
                r'(Get\s+it.*?Prime.*?)(?:\n|$)',
                r'(Prime\s+delivery.*?)(?:\n|$)',
                r'(Prime\s+FREE\s+delivery.*?)(?:\n|$)',
                r'(ONE[-\s]DAY.*?Prime.*?)(?:\n|$)',
                r'(TWO[-\s]DAY.*?Prime.*?)(?:\n|$)',
                r'(SAME\s+DAY.*?Prime.*?)(?:\n|$)'
            ]
            
            for pattern in prime_patterns:
                prime_match = re.search(pattern, container_text, re.IGNORECASE | re.MULTILINE)
                if prime_match:
                    prime_info = prime_match.group(1).strip()
                    if len(prime_info) > 5:  # 过滤太短的匹配
                        # 解析并打印配送时间
                        delivery_info = self._parse_delivery_time(prime_info)
                        if delivery_info['delivery_time']:
                            print(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                        return prime_info[:255]
            
            # 方法3: 检查是否简单包含Prime关键词
            if 'prime' in container_text.lower():
                # 寻找包含prime的句子
                lines = container_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if 'prime' in line.lower() and len(line) > 5 and len(line) < 100:
                        # 优先选择包含配送时间信息的行
                        if any(keyword in line.lower() for keyword in ['delivery', 'day', 'shipping', 'free', 'get it']):
                            delivery_info = self._parse_delivery_time(line)
                            if delivery_info['delivery_time']:
                                print(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                            return line[:255]
                
                # 如果没找到合适的句子，返回简单的Prime标识
                return 'Prime'
            
            return ''
            
        except Exception as e:
            print(f"⚠️ 提取Prime信息失败: {e}")
            return ''

    def _parse_delivery_time(self, text):
        """
        解析配送时间信息
        
        Args:
            text: 包含配送信息的文本
            
        Returns:
            dict: 配送时间信息
        """
        delivery_info = {
            'delivery_time': '',
            'delivery_type': '',
            'is_free': False
        }
        
        try:
            text_lower = text.lower()
            
            # 检查是否免费
            if any(word in text_lower for word in ['free', 'no charge', 'no cost']):
                delivery_info['is_free'] = True
            
            # 配送时间模式匹配
            delivery_patterns = [
                (r'one[-\s]day', 'ONE-DAY', 'Express'),
                (r'1[-\s]day', 'ONE-DAY', 'Express'),
                (r'two[-\s]day', 'TWO-DAY', 'Fast'),
                (r'2[-\s]day', 'TWO-DAY', 'Fast'),
                (r'same\s+day', 'SAME-DAY', 'Express'),
                (r'next\s+day', 'NEXT-DAY', 'Express'),
                (r'overnight', 'OVERNIGHT', 'Express'),
                (r'3[-\s]5\s+days?', '3-5 DAYS', 'Standard'),
                (r'5[-\s]7\s+days?', '5-7 DAYS', 'Standard'),
                (r'within\s+\d+\s+days?', 'MULTIPLE-DAY', 'Standard'),
                (r'tomorrow', 'TOMORROW', 'Express'),
                (r'today', 'TODAY', 'Express'),
                (r'\d+\s+business\s+days?', 'BUSINESS-DAYS', 'Standard')
            ]
            
            for pattern, delivery_time, delivery_type in delivery_patterns:
                if re.search(pattern, text_lower):
                    delivery_info['delivery_time'] = delivery_time
                    delivery_info['delivery_type'] = delivery_type
                    break
            
            # 特殊情况：提取具体数字天数
            if not delivery_info['delivery_time']:
                day_match = re.search(r'(\d+)[-\s]?days?', text_lower)
                if day_match:
                    days = int(day_match.group(1))
                    if days == 1:
                        delivery_info['delivery_time'] = 'ONE-DAY'
                        delivery_info['delivery_type'] = 'Express'
                    elif days == 2:
                        delivery_info['delivery_time'] = 'TWO-DAY'
                        delivery_info['delivery_type'] = 'Fast'
                    elif days <= 7:
                        delivery_info['delivery_time'] = f'{days}-DAY'
                        delivery_info['delivery_type'] = 'Standard'
                    else:
                        delivery_info['delivery_time'] = 'EXTENDED'
                        delivery_info['delivery_type'] = 'Standard'
            
        except Exception as e:
            print(f"⚠️ 解析配送时间失败: {e}")
        
        return delivery_info

    def analyze_prime_coverage(self, products):
        """分析Prime信息覆盖率 - 增强配送时间分析"""
        try:
            total = len(products)
            if total == 0:
                return {
                    'total': 0,
                    'with_prime': 0,
                    'coverage_rate': 0.0,
                    'prime_types': {},
                    'delivery_times': {}
                }
            
            with_prime = sum(1 for p in products if p.get('prime_info'))
            coverage_rate = (with_prime / total) * 100
            
            # 统计Prime类型和配送时间
            prime_types = {}
            delivery_times = {}
            
            for product in products:
                prime_info = product.get('prime_info', '')
                if prime_info:
                    # 简化Prime信息用于分类
                    if 'free delivery' in prime_info.lower():
                        prime_type = 'Free Delivery'
                    elif 'prime' in prime_info.lower():
                        prime_type = 'Prime'
                    else:
                        prime_type = 'Other'
                    
                    prime_types[prime_type] = prime_types.get(prime_type, 0) + 1
                    
                    # 分析配送时间
                    delivery_info = self._parse_delivery_time(prime_info)
                    delivery_time = delivery_info.get('delivery_time', 'Unknown')
                    if delivery_time:
                        delivery_times[delivery_time] = delivery_times.get(delivery_time, 0) + 1
            
            return {
                'total': total,
                'with_prime': with_prime,
                'coverage_rate': round(coverage_rate, 1),
                'prime_types': prime_types,
                'delivery_times': delivery_times
            }
            
        except Exception as e:
            print(f"❌ 分析Prime覆盖率失败: {e}")
            return {'total': 0, 'with_prime': 0, 'coverage_rate': 0.0, 'prime_types': {}, 'delivery_times': {}}

    def print_analysis_report(self, products, file_name):
        """打印分析报告 - 增强配送时间报告"""
        print(f"\n📊 {file_name} 分析报告")
        print("=" * 60)
        
        if not products:
            print("❌ 没有提取到商品数据")
            return
        
        # 基本统计
        print(f"📈 基本统计:")
        print(f"  总商品数: {len(products)}")
        
        # Prime信息分析
        prime_analysis = self.analyze_prime_coverage(products)
        print(f"\n🏷️ Prime信息分析:")
        print(f"  有Prime信息: {prime_analysis['with_prime']}")
        print(f"  覆盖率: {prime_analysis['coverage_rate']}%")
        
        if prime_analysis['prime_types']:
            print(f"  Prime类型分布:")
            for prime_type, count in prime_analysis['prime_types'].items():
                print(f"    {prime_type}: {count}")
        
        # 新增：配送时间分析
        if prime_analysis['delivery_times']:
            print(f"\n🚚 Prime配送时间分布:")
            sorted_delivery = sorted(prime_analysis['delivery_times'].items(), 
                                   key=lambda x: x[1], reverse=True)
            for delivery_time, count in sorted_delivery:
                percentage = (count / prime_analysis['with_prime']) * 100
                print(f"    {delivery_time}: {count} ({percentage:.1f}%)")
        
        # 价格分析
        prices = [float(p['current_price']) for p in products if p['current_price'] > 0]
        if prices:
            print(f"\n💰 价格分析:")
            print(f"  平均价格: ${sum(prices) / len(prices):.2f}")
            print(f"  价格范围: ${min(prices):.2f} - ${max(prices):.2f}")
        
        # 评分分析
        ratings = [float(p['rating']) for p in products if p['rating'] > 0]
        if ratings:
            print(f"\n⭐ 评分分析:")
            print(f"  平均评分: {sum(ratings) / len(ratings):.1f}")
            print(f"  评分范围: {min(ratings):.1f} - {max(ratings):.1f}")
        
        # 广告商品分析
        sponsored_count = sum(1 for p in products if p.get('is_sponsored', 0) == 1)
        print(f"\n📢 广告分析:")
        print(f"  广告商品: {sponsored_count}")
        print(f"  广告比例: {(sponsored_count / len(products) * 100):.1f}%")
        
        # 显示前5个商品的详细信息 - 增强配送时间显示
        print(f"\n📋 商品详情示例 (前5个):")
        for i, product in enumerate(products[:5], 1):
            print(f"\n  商品 {i}:")
            print(f"    ASIN: {product.get('asin', 'N/A')}")
            print(f"    标题: {product.get('title', 'N/A')[:50]}...")
            print(f"    价格: ${product.get('current_price', 0)}")
            print(f"    评分: {product.get('rating', 0)} ({product.get('review_count', 0)} reviews)")
            
            prime_info = product.get('prime_info', '')
            prime_status = "✅" if prime_info else "❌"
            
            # 分析配送时间
            if prime_info:
                delivery_info = self._parse_delivery_time(prime_info)
                delivery_time = delivery_info.get('delivery_time', 'N/A')
                delivery_type = delivery_info.get('delivery_type', '')
                is_free = "🆓" if delivery_info.get('is_free') else ""
                print(f"    Prime: {prime_status} {prime_info[:30]}...")
                print(f"    配送时间: ⏱️ {delivery_time} ({delivery_type}) {is_free}")
            else:
                print(f"    Prime: {prime_status}")
            
            print(f"    广告: {'是' if product.get('is_sponsored', 0) else '否'}")

    def _detect_sponsored(self, container):
        """检测是否为广告商品"""
        try:
            # 检查广告标签
            sponsored_elem = container.select_one(self.selectors['sponsored_label'])
            if sponsored_elem:
                return 1
            
            # 检查父容器的广告标识
            if container.find_parent('[data-component-type="sp-sponsored-result"]'):
                return 1
            
            # 检查文本内容
            container_text = container.get_text()
            if 'sponsored' in container_text.lower():
                return 1
            
            return 0
            
        except:
            return 0
    
    def _extract_bought_info(self, container):
        """提取购买数量信息"""
        try:
            container_text = container.get_text()
            
            # 匹配购买数量模式
            bought_patterns = [
                r'(\d+)\+?\s*bought\s*in\s*past\s*month',
                r'(\d+)K\+?\s*bought\s*in\s*past\s*month',
                r'(\d+)\+?\s*purchased\s*in\s*past\s*month',
                r'(\d+)K\+?\s*purchased\s*in\s*past\s*month'
            ]
            
            for pattern in bought_patterns:
                bought_match = re.search(pattern, container_text, re.IGNORECASE)
                if bought_match:
                    try:
                        bought_str = bought_match.group(1)
                        # 处理K单位
                        if 'K' in container_text.upper():
                            return int(float(bought_str) * 1000)
                        else:
                            return int(bought_str)
                    except:
                        continue
            
            return 0
            
        except:
            return 0

def test_single_file(file_path):
    """测试单个HTML文件"""
    print(f"🧪 测试文件: {file_path}")
    print("=" * 60)
    
    try:
        parser = OfflineAmazonParser()
        
        # 加载HTML文件
        soup = parser.load_html_file(file_path)
        if not soup:
            return False
        
        # 提取商品信息
        products = parser.extract_products_from_page(soup)
        
        # 打印分析报告
        file_name = os.path.basename(file_path)
        parser.print_analysis_report(products, file_name)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_files():
    """测试所有HTML文件"""
    print("🚀 离线测试模式 - 批量处理HTML文件")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        "doc/list.html",
        "doc/list2.html", 
        "doc/list3.html"
    ]
    
    results = {'total': 0, 'success': 0, 'failed': 0}
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            print(f"⚠️ 文件不存在，跳过: {file_path}")
            continue
        
        results['total'] += 1
        success = test_single_file(file_path)
        
        if success:
            results['success'] += 1
        else:
            results['failed'] += 1
        
        print()  # 空行分隔
    
    # 总结
    print("✅ 批量测试完成!")
    print("=" * 50)
    print(f"总文件数: {results['total']}")
    print(f"成功: {results['success']}")
    print(f"失败: {results['failed']}")
    
    if results['success'] > 0:
        print(f"\n📋 建议:")
        print(f"1. 检查Prime信息提取的准确性")
        print(f"2. 对比不同文件的提取结果")
        print(f"3. 调试改进提取算法")
    
    return results

def compare_with_online_crawler():
    """与在线爬虫进行对比测试"""
    print(f"\n🔄 对比测试: 离线模式 vs 在线爬虫")
    print("=" * 60)
    
    try:
        # 导入在线爬虫
        from test_crawler import AmazonCrawler
        
        # 创建爬虫实例
        crawler = AmazonCrawler()
        
        # 离线测试
        print("📋 步骤1: 离线模式测试")
        parser = OfflineAmazonParser()
        
        test_file = "doc/list2.html"
        if not os.path.exists(test_file):
            print(f"❌ 测试文件不存在: {test_file}")
            return False
        
        # 离线提取
        soup = parser.load_html_file(test_file)
        offline_products = parser.extract_products_from_page(soup)
        
        # 在线爬虫测试相同文件
        print("📋 步骤2: 在线爬虫离线模式测试")
        online_products = crawler.extract_products_from_offline_html(test_file)
        
        # 对比结果
        print("📋 步骤3: 结果对比")
        print(f"离线模式提取: {len(offline_products)} 个商品")
        print(f"在线爬虫提取: {len(online_products)} 个商品")
        
        # Prime信息对比
        offline_prime = sum(1 for p in offline_products if p.get('prime_info'))
        online_prime = sum(1 for p in online_products if p.get('prime_info'))
        
        # 防止除零错误
        if len(offline_products) > 0:
            offline_prime_rate = offline_prime / len(offline_products) * 100
            print(f"离线模式Prime覆盖: {offline_prime}/{len(offline_products)} ({offline_prime_rate:.1f}%)")
        else:
            print(f"离线模式Prime覆盖: {offline_prime}/0 (0.0%)")
        
        if len(online_products) > 0:
            online_prime_rate = online_prime / len(online_products) * 100
            print(f"在线爬虫Prime覆盖: {online_prime}/{len(online_products)} ({online_prime_rate:.1f}%)")
        else:
            print(f"在线爬虫Prime覆盖: {online_prime}/0 (0.0%)")
        
        # 检查一致性
        if len(offline_products) == len(online_products):
            print("✅ 商品数量一致")
        else:
            print("⚠️ 商品数量不一致")
            if len(online_products) == 0:
                print("💡 建议检查在线爬虫的辅助方法是否完整")
        
        # 显示详细对比
        if len(online_products) > 0 and len(offline_products) > 0:
            print("\n📊 详细对比:")
            print(f"功能完整性: ✅")
            
            # 比较前3个商品的Prime信息
            print("\n🔍 Prime信息对比示例:")
            max_compare = min(3, len(offline_products), len(online_products))
            for i in range(max_compare):
                offline_prime = offline_products[i].get('prime_info', '')
                online_prime = online_products[i].get('prime_info', '')
                print(f"  商品 {i+1}:")
                print(f"    离线: {offline_prime[:30]}...")
                print(f"    在线: {online_prime[:30]}...")
                match_status = "✅" if offline_prime == online_prime else "⚠️"
                print(f"    匹配: {match_status}")
        
        return True
        
    except ImportError:
        print("❌ 无法导入在线爬虫模块")
        return False
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_prime_extraction_compatibility():
    """验证Prime信息提取的兼容性"""
    print(f"\n🔍 验证Prime信息提取兼容性")
    print("=" * 60)
    
    try:
        parser = OfflineAmazonParser()
        
        # 测试不同的HTML文件
        test_files = ["doc/list.html", "doc/list2.html"]
        
        for file_path in test_files:
            if not os.path.exists(file_path):
                continue
                
            print(f"\n📋 测试文件: {os.path.basename(file_path)}")
            
            soup = parser.load_html_file(file_path)
            products = parser.extract_products_from_page(soup)
            
            if not products:
                print("❌ 没有提取到商品")
                continue
            
            # 分析Prime信息质量
            prime_products = [p for p in products if p.get('prime_info')]
            
            print(f"  商品总数: {len(products)}")
            print(f"  有Prime信息: {len(prime_products)}")
            
            if prime_products:
                print("  Prime信息示例:")
                for i, product in enumerate(prime_products[:3], 1):
                    prime_info = product['prime_info']
                    print(f"    {i}. {prime_info[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数 - 增强版"""
    try:
        # 原有的测试功能
        if len(sys.argv) > 1:
            file_path = sys.argv[1]
            return 0 if test_single_file(file_path) else 1
        else:
            # 批量测试
            results = test_all_files()
            
            # 新增功能：与在线爬虫对比
            print("\n" + "="*60)
            compare_with_online_crawler()
            
            # 新增功能：Prime提取兼容性验证
            validate_prime_extraction_compatibility()
            
            return 0 if results['failed'] == 0 else 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
        return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
