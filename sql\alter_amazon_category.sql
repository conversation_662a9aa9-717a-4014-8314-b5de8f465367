-- 为 zz_amazon_products_spu 表添加价格、评论、配送时间相关字段

ALTER TABLE zz_amazon_products_spu 
ADD COLUMN price DECIMAL(10,2) DEFAULT NULL COMMENT '产品价格',
ADD COLUMN currency VARCHAR(10) DEFAULT NULL COMMENT '货币类型',
ADD COLUMN rating DECIMAL(3,2) DEFAULT NULL COMMENT '评分',
ADD COLUMN review_count INT DEFAULT NULL COMMENT '评论数量',
ADD COLUMN prime_delivery VARCHAR(100) DEFAULT NULL COMMENT 'Prime配送时间',
ADD COLUMN delivery_info TEXT DEFAULT NULL COMMENT '配送信息详情',
ADD COLUMN availability VARCHAR(50) DEFAULT NULL COMMENT '库存状态',
ADD COLUMN image_url VARCHAR(500) DEFAULT NULL COMMENT '产品图片URL',
ADD COLUMN product_title VARCHAR(500) DEFAULT NULL COMMENT '产品标题',
ADD COLUMN asin VARCHAR(20) DEFAULT NULL COMMENT 'Amazon产品ASIN',
ADD COLUMN product_url VARCHAR(500) DEFAULT NULL COMMENT '产品链接',
ADD COLUMN last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间';

-- 为常用查询字段添加索引
CREATE INDEX idx_zz_amazon_products_spu_price ON zz_amazon_products_spu(price);
CREATE INDEX idx_zz_amazon_products_spu_rating ON zz_amazon_products_spu(rating);
CREATE INDEX idx_zz_amazon_products_spu_asin ON zz_amazon_products_spu(asin);
CREATE INDEX idx_zz_amazon_products_spu_updated ON zz_amazon_products_spu(last_updated);
