#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon爬虫配置文件
"""

import os

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', '**************'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'xh884813@@@XH'),
    'database': os.getenv('DB_NAME', 'xace200_lsh'),
    'port': 22888,
    'charset': 'utf8mb4',
    'autocommit': True
}


# 爬虫基础配置（优化后的503错误防护配置）
CRAWLER_CONFIG = {
    # 线程配置（降低并发避免503错误）
    'max_workers': int(os.getenv('MAX_WORKERS', '3')),           # 最大线程数（降低到3）
    'task_batch_size': int(os.getenv('TASK_BATCH_SIZE', '50')),  # 每次加载的任务数量（减少）

    # 请求配置
    'request_timeout': int(os.getenv('REQUEST_TIMEOUT', '30')),  # 请求超时时间（秒）
    'retry_count': int(os.getenv('RETRY_COUNT', '5')),           # 重试次数（增加）
    'retry_delay': (3, 8),                                       # 重试延迟范围（秒）（增加）

    # 延迟配置（进一步增加延迟避免503错误）
    'delay_range': (8, 15),                                      # 请求间隔范围（秒）（进一步增加）
    'page_delay_range': (15, 30),                                # 页面间隔范围（秒）（大幅增加）
    'task_delay_range': (20, 40),                                # 任务间隔范围（秒）（增加）
    'error_delay_range': (60, 120),                              # 错误后延迟范围（秒）（大幅增加）
    
    # 功能开关
    'use_proxy': os.getenv('USE_PROXY', 'true').lower() == 'true',          # 是否使用代理
    'rotate_user_agent': os.getenv('ROTATE_USER_AGENT', 'true').lower() == 'true',  # 是否轮换用户代理
    'enable_health_check': os.getenv('ENABLE_HEALTH_CHECK', 'true').lower() == 'true',  # 是否启用健康检查
    
    # 反爬虫配置（强化503错误防护）
    'max_captcha_retries': int(os.getenv('MAX_CAPTCHA_RETRIES', '2')),  # 验证码最大重试次数（减少）
    'captcha_delay_range': (60, 120),                                   # 遇到验证码的延迟范围（秒）（增加）
    'rate_limit_delay': (120, 300),                                     # 限流延迟范围（秒）（增加）
    'error_503_delay': (60, 180),                                       # 503错误延迟范围（秒）（新增）
    'max_503_retries': 3,                                               # 503错误最大重试次数（新增）

    # 数据配置（限制页数避免深度抓取）
    'max_pages_per_task': int(os.getenv('MAX_PAGES_PER_TASK', '30')),   # 每个任务最大页数（限制到30）
    'safe_page_limit': int(os.getenv('SAFE_PAGE_LIMIT', '50')),         # 安全页数限制（新增）
    'min_products_per_page': int(os.getenv('MIN_PRODUCTS_PER_PAGE', '5')),  # 每页最少商品数
    'save_batch_size': int(os.getenv('SAVE_BATCH_SIZE', '30')),         # 批量保存大小（减少）
}

# 日志配置
LOG_CONFIG = {
    'log_level': os.getenv('LOG_LEVEL', 'INFO'),
    'log_dir': os.getenv('LOG_DIR', 'logs'),
    'main_log_file': 'amazon_crawler.log',
    'error_log_file': 'amazon_crawler_error.log',
    'max_log_size': 50 * 1024 * 1024,  # 50MB
    'backup_count': 5,
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
}

# 代理配置
PROXY_CONFIG = {
    'proxy_file': os.getenv('PROXY_FILE', 'proxies.txt'),       # 代理文件路径
    'proxy_check_interval': int(os.getenv('PROXY_CHECK_INTERVAL', '300')),  # 代理检查间隔（秒）
    'proxy_timeout': int(os.getenv('PROXY_TIMEOUT', '10')),     # 代理超时时间（秒）
    'max_proxy_failures': int(os.getenv('MAX_PROXY_FAILURES', '3')),  # 代理最大失败次数
    'proxy_rotation_interval': int(os.getenv('PROXY_ROTATION_INTERVAL', '10')),  # 代理轮换间隔（请求数）
}

# Amazon特定配置
AMAZON_CONFIG = {
    'base_url': 'https://www.amazon.com',
    'search_domains': [
        'amazon.com',
        'amazon.co.uk',
        'amazon.de',
        'amazon.fr',
        'amazon.it',
        'amazon.es'
    ],
    
    # 页面选择器
    'selectors': {
        'product_container': '[data-component-type="s-search-result"]',
        'next_page': 'a.s-pagination-next',
        'product_title': 'h2 a span, [data-cy="title-recipe"] h2 span',
        'product_price': '.a-price .a-offscreen',
        'product_rating': '.a-icon-alt',
        'product_reviews': 'a[aria-label*="ratings"]',
        'product_image': 'img.s-image',
        'product_link': 'h2 a',
        'prime_badge': '[aria-label*="Prime"], .a-icon-prime',
        'sponsored_label': '.s-sponsored-label, [aria-label*="Sponsored"]'
    },
    
    # 反爬虫检测关键词
    'anti_bot_keywords': [
        'Robot Check',
        'captcha',
        'blocked',
        'unusual traffic',
        'verify you are human',
        'security check'
    ],
    
    # 价格模式
    'price_patterns': [
        r'List:\s*\$?([\d,]+\.?\d*)',
        r'Typical.*?:\s*\$?([\d,]+\.?\d*)',
        r'Was:\s*\$?([\d,]+\.?\d*)',
        r'Originally:\s*\$?([\d,]+\.?\d*)'
    ],
    
    # 购买量模式
    'bought_patterns': [
        r'(\d+)\+?\s*bought\s*in\s*past\s*month',
        r'(\d+)K\+?\s*bought\s*in\s*past\s*month',
        r'(\d+)\+?\s*purchased\s*in\s*past\s*month',
        r'(\d+)K\+?\s*purchased\s*in\s*past\s*month'
    ]
}

# 监控配置
MONITOR_CONFIG = {
    'enable_monitoring': os.getenv('ENABLE_MONITORING', 'true').lower() == 'true',
    'stats_interval': int(os.getenv('STATS_INTERVAL', '60')),    # 统计输出间隔（秒）
    'health_check_interval': int(os.getenv('HEALTH_CHECK_INTERVAL', '300')),  # 健康检查间隔（秒）
    'alert_thresholds': {
        'error_rate': float(os.getenv('ERROR_RATE_THRESHOLD', '0.1')),  # 错误率阈值
        'success_rate': float(os.getenv('SUCCESS_RATE_THRESHOLD', '0.8')),  # 成功率阈值
        'response_time': float(os.getenv('RESPONSE_TIME_THRESHOLD', '10.0')),  # 响应时间阈值（秒）
    }
}

# 性能配置
PERFORMANCE_CONFIG = {
    'enable_caching': os.getenv('ENABLE_CACHING', 'false').lower() == 'true',
    'cache_size': int(os.getenv('CACHE_SIZE', '1000')),
    'cache_ttl': int(os.getenv('CACHE_TTL', '3600')),  # 缓存TTL（秒）
    
    'enable_compression': os.getenv('ENABLE_COMPRESSION', 'true').lower() == 'true',
    'max_memory_usage': int(os.getenv('MAX_MEMORY_USAGE', '1024')),  # 最大内存使用（MB）
    
    'connection_pool_size': int(os.getenv('CONNECTION_POOL_SIZE', '10')),
    'max_retries_per_session': int(os.getenv('MAX_RETRIES_PER_SESSION', '5')),
}

# 开发/调试配置
DEBUG_CONFIG = {
    'debug_mode': os.getenv('DEBUG_MODE', 'false').lower() == 'true',
    'save_html': os.getenv('SAVE_HTML', 'false').lower() == 'true',
    'html_save_dir': os.getenv('HTML_SAVE_DIR', 'debug_html'),
    'log_requests': os.getenv('LOG_REQUESTS', 'false').lower() == 'true',
    'log_responses': os.getenv('LOG_RESPONSES', 'false').lower() == 'true',
    'dry_run': os.getenv('DRY_RUN', 'false').lower() == 'true',  # 只测试不保存
}

def get_config():
    """获取完整配置"""
    return {
        'db': DB_CONFIG,
        'crawler': CRAWLER_CONFIG,
        'log': LOG_CONFIG,
        'proxy': PROXY_CONFIG,
        'amazon': AMAZON_CONFIG,
        'monitor': MONITOR_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'debug': DEBUG_CONFIG
    }

def validate_config():
    """验证配置"""
    errors = []
    
    # 验证数据库配置
    required_db_fields = ['host', 'user', 'password', 'database']
    for field in required_db_fields:
        if not DB_CONFIG.get(field):
            errors.append(f"数据库配置缺少必需字段: {field}")
    
    # 验证线程数
    if CRAWLER_CONFIG['max_workers'] <= 0:
        errors.append("max_workers 必须大于 0")
    
    if CRAWLER_CONFIG['max_workers'] > 20:
        errors.append("max_workers 不建议超过 20")
    
    # 验证超时时间
    if CRAWLER_CONFIG['request_timeout'] <= 0:
        errors.append("request_timeout 必须大于 0")
    
    # 验证延迟配置
    for delay_key in ['delay_range', 'page_delay_range', 'task_delay_range']:
        delay_range = CRAWLER_CONFIG[delay_key]
        if not isinstance(delay_range, tuple) or len(delay_range) != 2:
            errors.append(f"{delay_key} 必须是包含两个元素的元组")
        elif delay_range[0] >= delay_range[1]:
            errors.append(f"{delay_key} 的最小值必须小于最大值")
    
    return errors

def print_config():
    """打印配置信息"""
    print("=" * 60)
    print("Amazon爬虫配置信息")
    print("=" * 60)
    
    print(f"数据库: {DB_CONFIG['host']}:{DB_CONFIG.get('port', 3306)}/{DB_CONFIG['database']}")
    print(f"最大线程数: {CRAWLER_CONFIG['max_workers']}")
    print(f"请求超时: {CRAWLER_CONFIG['request_timeout']}秒")
    print(f"使用代理: {'是' if CRAWLER_CONFIG['use_proxy'] else '否'}")
    print(f"轮换用户代理: {'是' if CRAWLER_CONFIG['rotate_user_agent'] else '否'}")
    print(f"调试模式: {'是' if DEBUG_CONFIG['debug_mode'] else '否'}")
    print(f"日志级别: {LOG_CONFIG['log_level']}")
    
    print("=" * 60)

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
        print_config()
