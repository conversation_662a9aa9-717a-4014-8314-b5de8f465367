#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理监控工具
实时显示代理使用情况和切换统计
"""

import time
import threading
import pymysql
from datetime import datetime, timedelta
from collections import defaultdict
from crawler_config import get_config
from proxy_manager import FyndiqProxyManager

class ProxyMonitor:
    """代理监控器"""
    
    def __init__(self):
        self.config = get_config()
        self.proxy_manager = FyndiqProxyManager(self.config['db'])
        self.running = False
        self.monitor_thread = None
        
        # 统计数据
        self.proxy_stats = defaultdict(lambda: {
            'success': 0,
            'failed': 0,
            'last_used': None,
            'response_times': []
        })
        
        self.task_proxy_mapping = {}  # 任务到代理的映射
        self.proxy_switches = 0
        
    def start_monitoring(self, interval=30):
        """启动监控"""
        self.running = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"🔍 代理监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        print("🛑 代理监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.running:
            try:
                self.display_proxy_status()
                time.sleep(interval)
            except Exception as e:
                print(f"❌ 监控出错: {e}")
                time.sleep(interval)
    
    def display_proxy_status(self):
        """显示代理状态"""
        print("\n" + "=" * 80)
        print(f"🔍 代理监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 1. 显示代理池状态
        self.display_proxy_pool_status()
        
        # 2. 显示任务代理映射
        self.display_task_proxy_mapping()
        
        # 3. 显示代理使用统计
        self.display_proxy_usage_stats()
        
        # 4. 显示代理性能排行
        self.display_proxy_performance()
        
        print("=" * 80)
    
    def display_proxy_pool_status(self):
        """显示代理池状态"""
        print("\n📊 代理池状态:")
        
        try:
            # 从数据库获取代理信息
            proxies = self.proxy_manager.load_proxies()
            
            if not proxies:
                print("  ❌ 没有可用代理")
                return
            
            total_proxies = len(proxies)
            active_proxies = len([p for p in proxies if p.get('is_active', True)])
            
            print(f"  总代理数: {total_proxies}")
            print(f"  活跃代理: {active_proxies}")
            print(f"  可用率: {active_proxies/total_proxies*100:.1f}%")
            
            # 显示代理地区分布
            regions = defaultdict(int)
            for proxy in proxies:
                # 简单的地区判断（基于IP段）
                ip = proxy.get('ip', '')
                if ip.startswith('192.168') or ip.startswith('10.') or ip.startswith('172.'):
                    region = '内网'
                elif ip.startswith('1.'):
                    region = '亚洲'
                elif ip.startswith('2.'):
                    region = '欧洲'
                else:
                    region = '其他'
                regions[region] += 1
            
            if regions:
                print("  地区分布:")
                for region, count in regions.items():
                    print(f"    {region}: {count}个")
        
        except Exception as e:
            print(f"  ❌ 获取代理池状态失败: {e}")
    
    def display_task_proxy_mapping(self):
        """显示任务代理映射"""
        print("\n🔗 任务代理映射:")
        
        try:
            # 从数据库获取进行中的任务
            conn = pymysql.connect(**self.config['db'])
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute("""
                SELECT id, source_category_id, crawled_pages, max_pages_to_crawl, updated_at
                FROM zz_amazon_list_tasks 
                WHERE status = 'in_progress'
                ORDER BY updated_at DESC
                LIMIT 10
            """)
            
            active_tasks = cursor.fetchall()
            conn.close()
            
            if not active_tasks:
                print("  📋 没有进行中的任务")
                return
            
            print(f"  活跃任务数: {len(active_tasks)}")
            
            for task in active_tasks:
                task_id = task['id']
                progress = f"{task['crawled_pages']}/{task['max_pages_to_crawl']}"
                last_update = task['updated_at']
                
                # 模拟代理映射（实际应该从爬虫实例获取）
                proxy_ip = self.task_proxy_mapping.get(task_id, "未知")
                
                print(f"    任务 {task_id}: {progress} 页 | 代理: {proxy_ip} | 更新: {last_update}")
        
        except Exception as e:
            print(f"  ❌ 获取任务映射失败: {e}")
    
    def display_proxy_usage_stats(self):
        """显示代理使用统计"""
        print("\n📈 代理使用统计:")
        
        if not self.proxy_stats:
            print("  📊 暂无使用统计")
            return
        
        # 计算总体统计
        total_success = sum(stats['success'] for stats in self.proxy_stats.values())
        total_failed = sum(stats['failed'] for stats in self.proxy_stats.values())
        total_requests = total_success + total_failed
        
        if total_requests == 0:
            print("  📊 暂无请求记录")
            return
        
        overall_success_rate = (total_success / total_requests) * 100
        
        print(f"  总请求数: {total_requests}")
        print(f"  成功请求: {total_success}")
        print(f"  失败请求: {total_failed}")
        print(f"  整体成功率: {overall_success_rate:.1f}%")
        print(f"  代理切换次数: {self.proxy_switches}")
        print(f"  使用的代理数: {len(self.proxy_stats)}")
    
    def display_proxy_performance(self):
        """显示代理性能排行"""
        print("\n🏆 代理性能排行 (前10名):")
        
        if not self.proxy_stats:
            print("  📊 暂无性能数据")
            return
        
        # 按成功率和请求数排序
        proxy_performance = []
        for proxy_ip, stats in self.proxy_stats.items():
            total_requests = stats['success'] + stats['failed']
            if total_requests > 0:
                success_rate = (stats['success'] / total_requests) * 100
                avg_response_time = sum(stats['response_times']) / len(stats['response_times']) if stats['response_times'] else 0
                
                proxy_performance.append({
                    'ip': proxy_ip,
                    'success_rate': success_rate,
                    'total_requests': total_requests,
                    'avg_response_time': avg_response_time,
                    'last_used': stats['last_used']
                })
        
        # 排序：优先成功率，其次请求数
        proxy_performance.sort(key=lambda x: (x['success_rate'], x['total_requests']), reverse=True)
        
        for i, proxy in enumerate(proxy_performance[:10], 1):
            print(f"  {i:2d}. {proxy['ip']:15s} | "
                  f"成功率: {proxy['success_rate']:5.1f}% | "
                  f"请求数: {proxy['total_requests']:4d} | "
                  f"响应时间: {proxy['avg_response_time']:5.2f}s")
    
    def record_proxy_usage(self, proxy_ip, success=True, response_time=None):
        """记录代理使用情况"""
        now = datetime.now()
        
        if success:
            self.proxy_stats[proxy_ip]['success'] += 1
        else:
            self.proxy_stats[proxy_ip]['failed'] += 1
        
        self.proxy_stats[proxy_ip]['last_used'] = now
        
        if response_time is not None:
            self.proxy_stats[proxy_ip]['response_times'].append(response_time)
            # 只保留最近100次的响应时间
            if len(self.proxy_stats[proxy_ip]['response_times']) > 100:
                self.proxy_stats[proxy_ip]['response_times'].pop(0)
    
    def record_proxy_switch(self, task_id, old_proxy, new_proxy):
        """记录代理切换"""
        self.proxy_switches += 1
        self.task_proxy_mapping[task_id] = new_proxy
        print(f"🔄 任务 {task_id} 代理切换: {old_proxy} → {new_proxy}")
    
    def get_proxy_recommendations(self):
        """获取代理使用建议"""
        recommendations = []
        
        if not self.proxy_stats:
            return ["暂无足够数据提供建议"]
        
        # 分析代理性能
        total_requests = sum(stats['success'] + stats['failed'] for stats in self.proxy_stats.values())
        if total_requests == 0:
            return ["暂无请求数据"]
        
        # 计算平均成功率
        success_rates = []
        for stats in self.proxy_stats.values():
            total = stats['success'] + stats['failed']
            if total > 0:
                success_rates.append(stats['success'] / total)
        
        if success_rates:
            avg_success_rate = sum(success_rates) / len(success_rates) * 100
            
            if avg_success_rate < 80:
                recommendations.append(f"整体成功率较低 ({avg_success_rate:.1f}%)，建议检查代理质量")
            
            if self.proxy_switches > total_requests * 0.1:
                recommendations.append("代理切换频繁，可能存在代理稳定性问题")
            
            if len(self.proxy_stats) < 5:
                recommendations.append("使用的代理数量较少，建议增加代理池")
        
        if not recommendations:
            recommendations.append("代理使用状况良好")
        
        return recommendations

def main():
    """主函数"""
    print("🚀 Amazon爬虫代理监控工具")
    print("=" * 60)
    
    monitor = ProxyMonitor()
    
    try:
        # 启动监控
        monitor.start_monitoring(interval=30)
        
        print("📋 监控功能:")
        print("1. 代理池状态监控")
        print("2. 任务代理映射显示")
        print("3. 代理使用统计")
        print("4. 代理性能排行")
        print("\n按 Ctrl+C 停止监控...")
        
        # 保持运行
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        monitor.stop_monitoring()
        
        # 显示最终建议
        print("\n💡 代理使用建议:")
        recommendations = monitor.get_proxy_recommendations()
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    except Exception as e:
        print(f"❌ 监控出错: {e}")
        monitor.stop_monitoring()

if __name__ == "__main__":
    main()
