#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试实际爬取过程中的Prime信息提取问题
"""

import sys
import os
import time
from datetime import datetime
from bs4 import BeautifulSoup

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_crawler import AmazonCrawler
from crawler_config import get_config

def debug_single_request():
    """调试单个请求的Prime信息提取"""
    print("🔍 调试单个请求的Prime信息提取")
    print("=" * 60)
    
    try:
        # 创建爬虫实例
        config = get_config()
        crawler = AmazonCrawler(config)
        
        # 测试URL
        test_url = "https://www.amazon.com/s?k=party+decorations&i=toys-and-games&rh=p_76%3A2661625011"
        
        print(f"📋 测试URL: {test_url}")
        
        # 发送请求
        print("📡 发送请求...")
        response = crawler.make_request(test_url)
        
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
        
        print(f"✅ 请求成功，状态码: {response.status_code}")
        print(f"📊 响应大小: {len(response.text)} 字符")
        
        # 保存响应内容用于调试
        with open('debug_live_response.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("💾 响应内容已保存到 debug_live_response.html")
        
        # 解析页面
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 检查页面是否被反爬虫拦截
        if "Robot Check" in response.text or "captcha" in response.text.lower():
            print("❌ 检测到反爬虫验证页面")
            return False
        
        # 查找商品容器
        product_containers = soup.select('[data-component-type="s-search-result"]')
        print(f"🔍 找到 {len(product_containers)} 个商品容器")
        
        if not product_containers:
            print("❌ 没有找到商品容器")
            # 尝试其他选择器
            alt_containers = soup.select('div[data-asin]')
            print(f"🔍 备用选择器找到 {len(alt_containers)} 个容器")
            return False
        
        # 提取前3个商品的详细信息
        print(f"\n📋 详细分析前3个商品:")
        
        for i, container in enumerate(product_containers[:3], 1):
            print(f"\n--- 商品 {i} ---")
            
            # 提取ASIN
            asin = container.get('data-asin', '')
            print(f"ASIN: {asin}")
            
            # 提取标题
            title_elem = container.select_one('h2 a span, h2 span')
            title = title_elem.get_text(strip=True) if title_elem else 'N/A'
            print(f"标题: {title[:50]}...")
            
            # 详细分析Prime信息
            print(f"Prime信息分析:")
            
            # 1. 检查容器文本内容
            container_text = container.get_text()
            has_prime_text = 'prime' in container_text.lower()
            print(f"  容器包含'prime'文本: {'✅' if has_prime_text else '❌'}")
            
            if has_prime_text:
                # 显示包含prime的文本片段
                lines = container_text.split('\n')
                prime_lines = [line.strip() for line in lines if 'prime' in line.lower() and line.strip()]
                print(f"  包含prime的文本片段:")
                for line in prime_lines[:3]:  # 显示前3个
                    print(f"    '{line}'")
            
            # 2. 检查Prime元素
            prime_selectors = [
                'i[aria-label*="Prime"]',
                '[aria-label*="Prime"]',
                '.a-icon-prime',
                'span[aria-label*="Prime"]'
            ]
            
            found_prime_element = False
            for selector in prime_selectors:
                prime_elem = container.select_one(selector)
                if prime_elem:
                    found_prime_element = True
                    prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                    print(f"  找到Prime元素 ({selector}): '{prime_text}'")
                    break
            
            if not found_prime_element:
                print(f"  ❌ 没有找到Prime元素")
            
            # 3. 使用爬虫的提取方法
            product_data = crawler.extract_single_product(container)
            if product_data:
                prime_info = product_data.get('prime_info', '')
                prime_status = "✅" if prime_info else "❌"
                print(f"  爬虫提取结果: {prime_status} '{prime_info}'")
            else:
                print(f"  ❌ 爬虫提取失败")
        
        # 使用爬虫的完整提取方法
        print(f"\n📊 使用爬虫完整提取方法:")
        products = crawler.extract_products_from_page(soup)
        
        if products:
            print(f"✅ 提取到 {len(products)} 个商品")
            
            prime_count = sum(1 for p in products if p.get('prime_info'))
            prime_rate = (prime_count / len(products)) * 100
            print(f"📈 Prime信息覆盖率: {prime_count}/{len(products)} ({prime_rate:.1f}%)")
            
            # 显示Prime信息示例
            print(f"📋 Prime信息示例:")
            for i, product in enumerate(products[:5], 1):
                asin = product.get('asin', 'N/A')
                prime_info = product.get('prime_info', '')
                prime_status = "✅" if prime_info else "❌"
                print(f"  商品{i} - ASIN: {asin}, Prime: {prime_status} '{prime_info}'")
        else:
            print(f"❌ 没有提取到商品")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_html_structures():
    """比较本地HTML和实际抓取的HTML结构"""
    print(f"\n🔍 比较HTML结构差异")
    print("=" * 60)
    
    try:
        # 检查本地HTML文件
        local_files = ["doc/list.html", "doc/list2.html"]
        live_file = "debug_live_response.html"
        
        if not os.path.exists(live_file):
            print(f"❌ 实际抓取的HTML文件不存在: {live_file}")
            return False
        
        print(f"📋 分析HTML结构差异:")
        
        for local_file in local_files:
            if not os.path.exists(local_file):
                continue
            
            print(f"\n--- {local_file} vs 实际抓取 ---")
            
            # 读取本地文件
            with open(local_file, 'r', encoding='utf-8') as f:
                local_html = f.read()
            
            # 读取实际抓取文件
            with open(live_file, 'r', encoding='utf-8') as f:
                live_html = f.read()
            
            # 解析HTML
            local_soup = BeautifulSoup(local_html, 'html.parser')
            live_soup = BeautifulSoup(live_html, 'html.parser')
            
            # 比较商品容器数量
            local_containers = local_soup.select('[data-component-type="s-search-result"]')
            live_containers = live_soup.select('[data-component-type="s-search-result"]')
            
            print(f"  商品容器数量:")
            print(f"    本地文件: {len(local_containers)}")
            print(f"    实际抓取: {len(live_containers)}")
            
            # 比较Prime信息
            if local_containers and live_containers:
                # 分析第一个商品的Prime信息
                local_container = local_containers[0]
                live_container = live_containers[0]
                
                local_text = local_container.get_text()
                live_text = live_container.get_text()
                
                local_has_prime = 'prime' in local_text.lower()
                live_has_prime = 'prime' in live_text.lower()
                
                print(f"  第一个商品Prime信息:")
                print(f"    本地文件包含prime: {'✅' if local_has_prime else '❌'}")
                print(f"    实际抓取包含prime: {'✅' if live_has_prime else '❌'}")
                
                if local_has_prime and not live_has_prime:
                    print(f"  ⚠️ 发现差异: 本地文件有Prime信息，实际抓取没有")
                elif not local_has_prime and live_has_prime:
                    print(f"  ⚠️ 发现差异: 实际抓取有Prime信息，本地文件没有")
                elif local_has_prime and live_has_prime:
                    print(f"  ✅ 两者都包含Prime信息")
                else:
                    print(f"  ❌ 两者都不包含Prime信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 实际爬取Prime信息调试工具")
    print("=" * 60)
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 1. 调试单个请求
        debug_success = debug_single_request()
        
        # 2. 比较HTML结构
        if debug_success:
            compare_success = compare_html_structures()
        else:
            compare_success = False
        
        # 总结
        print(f"\n✅ 调试完成!")
        print("=" * 50)
        
        if debug_success:
            print("✅ 成功获取实际页面内容")
            if compare_success:
                print("✅ 完成HTML结构比较")
            
            print("\n📋 下一步操作:")
            print("1. 查看 debug_live_response.html 文件")
            print("2. 检查实际页面是否包含Prime信息")
            print("3. 对比本地HTML和实际HTML的差异")
            print("4. 根据差异调整Prime信息提取逻辑")
        else:
            print("❌ 获取实际页面内容失败")
            print("\n📋 可能原因:")
            print("1. 网络连接问题")
            print("2. 代理配置问题")
            print("3. Amazon反爬虫拦截")
        
        return 0 if debug_success else 1
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
