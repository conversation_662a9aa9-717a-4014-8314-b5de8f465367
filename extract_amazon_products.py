import requests
import time
import random
import json
import re
from bs4 import BeautifulSoup
import pymysql
from urllib.parse import urljoin, urlparse

# === 配置 ===
DB_CONFIG = {
    'host': '**************',
    'user': 'root',
    'password': 'xh884813@@@XH',
    'database': 'xace200_lsh',
    'port': 22888
}

# 测试模式：设置为 True 时只处理一条数据进行测试
TEST_MODE = True

HEADERS = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "upgrade-insecure-requests": "1",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "cache-control": "max-age=0"
}

def extract_product_info(soup, base_url=""):
    """从页面提取产品信息"""
    products = []
    
    # 查找产品容器
    product_containers = soup.find_all(['div'], {'data-component-type': 's-search-result'})
    
    if not product_containers:
        # 尝试其他选择器
        product_containers = soup.find_all(['div'], class_=re.compile(r's-result-item'))
    
    for container in product_containers:
        try:
            product = extract_single_product(container, base_url)
            if product:
                products.append(product)
        except Exception as e:
            print(f"提取单个产品信息失败: {e}")
            continue
    
    return products

def extract_single_product(container, base_url):
    """提取单个产品的详细信息"""
    product = {}
    
    try:
        # ASIN
        asin = container.get('data-asin', '')
        product['asin'] = asin
        
        # 产品标题
        title_elem = container.find('h2') or container.find('span', {'data-component-type': 's-size-mini'})
        if title_elem:
            # 获取文本内容，去除多余空格
            title_text = title_elem.get_text(strip=True)
            product['product_title'] = title_text[:500]  # 限制长度
        
        # 产品链接
        link_elem = container.find('a', href=True)
        if link_elem:
            href = link_elem['href']
            if href.startswith('/'):
                href = urljoin(base_url, href)
            product['product_url'] = href[:500]
        
        # 价格信息
        price_info = extract_price_info(container)
        product.update(price_info)
        
        # 评分和评论数
        rating_info = extract_rating_info(container)
        product.update(rating_info)
        
        # Prime配送信息
        delivery_info = extract_delivery_info(container)
        product.update(delivery_info)
        
        # 产品图片
        img_elem = container.find('img')
        if img_elem:
            img_src = img_elem.get('src') or img_elem.get('data-src')
            if img_src:
                product['image_url'] = img_src[:500]
        
        # 库存状态
        availability = extract_availability(container)
        product['availability'] = availability
        
        return product if product.get('asin') else None
        
    except Exception as e:
        print(f"提取产品信息时出错: {e}")
        return None

def extract_price_info(container):
    """提取价格信息"""
    price_info = {}
    
    # 尝试多种价格选择器
    price_selectors = [
        '.a-price-whole',
        '.a-price .a-offscreen',
        '.a-price-symbol + .a-price-whole',
        '[data-component-type="s-product-price"] .a-price'
    ]
    
    for selector in price_selectors:
        price_elem = container.select_one(selector)
        if price_elem:
            price_text = price_elem.get_text(strip=True)
            # 提取数字价格
            price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
            if price_match:
                try:
                    price_info['price'] = float(price_match.group())
                    # 提取货币符号
                    currency_match = re.search(r'[$¥€£]', price_text)
                    price_info['currency'] = currency_match.group() if currency_match else 'USD'
                    break
                except ValueError:
                    continue
    
    return price_info

def extract_rating_info(container):
    """提取评分和评论数信息"""
    rating_info = {}
    
    # 评分
    rating_elem = container.find('span', class_=re.compile(r'a-icon-alt'))
    if rating_elem:
        rating_text = rating_elem.get_text()
        rating_match = re.search(r'(\d+\.?\d*)\s*out of', rating_text)
        if rating_match:
            try:
                rating_info['rating'] = float(rating_match.group(1))
            except ValueError:
                pass
    
    # 评论数
    review_selectors = [
        'a[href*="#customerReviews"]',
        '.a-size-base[data-component-type="s-client-side-analytics"]'
    ]
    
    for selector in review_selectors:
        review_elem = container.select_one(selector)
        if review_elem:
            review_text = review_elem.get_text(strip=True)
            # 提取数字
            review_match = re.search(r'([\d,]+)', review_text.replace(',', ''))
            if review_match:
                try:
                    rating_info['review_count'] = int(review_match.group(1).replace(',', ''))
                    break
                except ValueError:
                    continue
    
    return rating_info

def extract_delivery_info(container):
    """提取配送信息"""
    delivery_info = {}
    
    # Prime 配送时间
    prime_selectors = [
        '[data-component-type="s-delivery-message"]',
        '.a-size-base-plus',
        '.s-size-mini',
        '.a-color-base'
    ]
    
    delivery_texts = []
    
    for selector in prime_selectors:
        elements = container.select(selector)
        for elem in elements:
            text = elem.get_text(strip=True)
            if any(keyword in text.lower() for keyword in ['delivery', 'prime', 'shipping', 'arrives']):
                delivery_texts.append(text)
    
    # 合并所有配送信息
    if delivery_texts:
        delivery_info['delivery_info'] = ' | '.join(delivery_texts[:3])  # 限制长度
        
        # 尝试提取Prime特定信息
        for text in delivery_texts:
            if 'prime' in text.lower():
                delivery_info['prime_delivery'] = text[:100]
                break
    
    return delivery_info

def extract_availability(container):
    """提取库存状态"""
    availability_keywords = ['in stock', 'out of stock', 'available', 'unavailable', 'limited']
    
    # 在整个容器中搜索库存相关文本
    container_text = container.get_text().lower()
    
    for keyword in availability_keywords:
        if keyword in container_text:
            return keyword.replace(' ', '_')
    
    return 'unknown'

def save_products_to_db(products):
    """保存产品信息到数据库"""
    if not products:
        return
    
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    sql = '''
        INSERT INTO zz_amazon_products_spu (
            asin, product_title, product_url, price, currency, rating, 
            review_count, prime_delivery, delivery_info, availability, 
            image_url, last_updated
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
        ON DUPLICATE KEY UPDATE
            product_title = VALUES(product_title),
            product_url = VALUES(product_url),
            price = VALUES(price),
            currency = VALUES(currency),
            rating = VALUES(rating),
            review_count = VALUES(review_count),
            prime_delivery = VALUES(prime_delivery),
            delivery_info = VALUES(delivery_info),
            availability = VALUES(availability),
            image_url = VALUES(image_url),
            last_updated = NOW()
    '''
    
    for product in products:
        values = (
            product.get('asin'),
            product.get('product_title'),
            product.get('product_url'),
            product.get('price'),
            product.get('currency'),
            product.get('rating'),
            product.get('review_count'),
            product.get('prime_delivery'),
            product.get('delivery_info'),
            product.get('availability'),
            product.get('image_url')
        )
        
        try:
            cursor.execute(sql, values)
            if TEST_MODE:
                print(f"保存产品: {product.get('product_title', 'Unknown')[:50]}")
        except Exception as e:
            print(f"保存产品失败: {e}")
    
    conn.commit()
    cursor.close()
    conn.close()

def process_html_file(file_path):
    """处理HTML文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        soup = BeautifulSoup(content, 'html.parser')
        
        # 提取基础URL
        base_url = "https://www.amazon.com"
        
        # 提取产品信息
        products = extract_product_info(soup, base_url)
        
        if TEST_MODE:
            print(f"提取到 {len(products)} 个产品")
            if products:
                print(f"第一个产品示例: {json.dumps(products[0], indent=2, ensure_ascii=False)}")
        
        # 保存到数据库
        if products:
            save_products_to_db(products)
            print(f"成功保存 {len(products)} 个产品到数据库")
        
        return products
        
    except Exception as e:
        print(f"处理HTML文件失败: {e}")
        return []

def create_products_table():
    """创建产品表"""
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    sql = '''
        CREATE TABLE IF NOT EXISTS zz_amazon_products_spu (
            id INT AUTO_INCREMENT PRIMARY KEY,
            asin VARCHAR(20) UNIQUE NOT NULL,
            product_title VARCHAR(500),
            product_url VARCHAR(500),
            price DECIMAL(10,2),
            currency VARCHAR(10),
            rating DECIMAL(3,2),
            review_count INT,
            prime_delivery VARCHAR(100),
            delivery_info TEXT,
            availability VARCHAR(50),
            image_url VARCHAR(500),
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_asin (asin),
            INDEX idx_price (price),
            INDEX idx_rating (rating),
            INDEX idx_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Amazon产品信息表';
    '''
    
    cursor.execute(sql)
    conn.commit()
    cursor.close()
    conn.close()

if __name__ == "__main__":
    if TEST_MODE:
        print("=== 测试模式启用 ===")
        print("处理本地HTML文件...")
    
    # 创建产品表
    create_products_table()
    
    # 处理HTML文件
    html_file_path = r"e:\extension\temu_category\doc\list.html"
    products = process_html_file(html_file_path)
    
    if TEST_MODE:
        print("测试完成。")
    else:
        print("处理完成。")
