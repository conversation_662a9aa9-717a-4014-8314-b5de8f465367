#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Amazon分页爬虫功能
使用本地HTML文件进行测试
"""

import os
from bs4 import BeautifulSoup
from amazon_list_crawler_with_pagination import (
    extract_next_page_url, get_pagination_info, 
    extract_products_from_list_page, build_page_url
)

def test_pagination_extraction():
    """测试分页信息提取功能"""
    
    print("🧪 测试分页信息提取功能")
    print("=" * 50)
    
    # 读取测试HTML文件
    html_file = "doc/list2.html"
    
    if not os.path.exists(html_file):
        print(f"❌ 测试文件不存在: {html_file}")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 测试分页信息提取
    print("📊 提取分页信息...")
    pagination_info = get_pagination_info(soup)
    
    print(f"   当前页: {pagination_info['current_page']}")
    print(f"   总页数: {pagination_info['total_pages']}")
    print(f"   有下一页: {pagination_info['has_next']}")
    
    # 测试下一页URL提取
    print("\n🔗 提取下一页URL...")
    current_url = "https://www.amazon.com/s?k=Kids%27+Party+Centerpieces&i=toys-and-games&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000&ref=sr_nr_p_36_1"
    next_url = extract_next_page_url(soup, current_url)
    
    if next_url:
        print(f"   下一页URL: {next_url}")
        
        # 验证URL格式
        if "page=2" in next_url:
            print("   ✅ URL格式正确，包含page=2参数")
        else:
            print("   ⚠️ URL格式可能有问题")
    else:
        print("   ❌ 未找到下一页URL")
    
    # 测试商品数据提取
    print("\n📦 提取商品数据...")
    products = extract_products_from_list_page(soup)
    
    print(f"   找到商品数量: {len(products)}")
    
    if products:
        print("   前3个商品信息:")
        for i, product in enumerate(products[:3], 1):
            print(f"      商品 {i}:")
            print(f"         ASIN: {product.get('asin', 'N/A')}")
            print(f"         标题: {product.get('title', 'N/A')[:50]}...")
            print(f"         价格: ${product.get('price', 'N/A')}")
            print(f"         评分: {product.get('rating', 'N/A')}")
            print(f"         评论数: {product.get('review_count', 'N/A')}")
            print()

def test_url_building():
    """测试URL构建功能"""
    
    print("🔧 测试URL构建功能")
    print("=" * 50)
    
    base_url = "https://www.amazon.com/s?k=Kids%27+Party+Centerpieces&i=toys-and-games&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000&ref=sr_nr_p_36_1"
    
    print(f"基础URL: {base_url}")
    print()
    
    # 测试构建不同页码的URL
    for page in [1, 2, 3, 5, 10]:
        page_url = build_page_url(base_url, page)
        print(f"第 {page} 页URL: {page_url}")
        
        # 验证URL
        if page == 1:
            if "page=" not in page_url:
                print(f"   ✅ 第1页URL正确（无page参数）")
            else:
                print(f"   ⚠️ 第1页URL可能有问题（包含page参数）")
        else:
            if f"page={page}" in page_url:
                print(f"   ✅ 第{page}页URL正确")
            else:
                print(f"   ❌ 第{page}页URL错误")
        print()

def test_pagination_navigation():
    """测试分页导航逻辑"""
    
    print("🧭 测试分页导航逻辑")
    print("=" * 50)
    
    # 模拟分页抓取流程
    base_url = "https://www.amazon.com/s?k=Kids%27+Party+Centerpieces&i=toys-and-games"
    max_pages = 5
    
    print(f"模拟抓取流程:")
    print(f"   基础URL: {base_url}")
    print(f"   最大页数: {max_pages}")
    print()
    
    current_url = base_url
    current_page = 1
    
    while current_page <= max_pages:
        print(f"📄 第 {current_page} 页:")
        print(f"   URL: {current_url}")
        
        # 模拟检查是否有下一页
        has_next = current_page < max_pages  # 简化的逻辑
        
        if has_next and current_page < max_pages:
            next_page = current_page + 1
            next_url = build_page_url(base_url, next_page)
            print(f"   下一页: {next_url}")
            
            current_url = next_url
            current_page = next_page
        else:
            print(f"   ✅ 已到达最后一页或达到最大页数限制")
            break
        
        print()

def main():
    """主测试函数"""
    
    print("🚀 Amazon分页爬虫测试")
    print("=" * 60)
    
    try:
        # 测试分页信息提取
        test_pagination_extraction()
        print()
        
        # 测试URL构建
        test_url_building()
        print()
        
        # 测试分页导航逻辑
        test_pagination_navigation()
        
        print("✅ 所有测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
