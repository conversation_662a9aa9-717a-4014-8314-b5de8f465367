#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Amazon分页爬虫与详情页任务队列的集成
"""

import os
import json
from bs4 import BeautifulSoup
from amazon_list_crawler_with_pagination import (
    extract_products_from_list_page, save_products_to_page_tasks,
    create_list_task, get_task_status
)

def test_product_extraction_with_enhanced_data():
    """测试增强的商品数据提取（包含Prime和广告信息）"""
    
    print("🧪 测试增强的商品数据提取")
    print("=" * 50)
    
    # 读取测试HTML文件
    html_file = "doc/list2.html"
    
    if not os.path.exists(html_file):
        print(f"❌ 测试文件不存在: {html_file}")
        return []
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 提取商品数据
    print("📦 提取商品数据...")
    products = extract_products_from_list_page(soup)
    
    print(f"   找到商品数量: {len(products)}")
    
    if products:
        print("\n📋 前5个商品的详细信息:")
        for i, product in enumerate(products[:5], 1):
            print(f"   商品 {i}:")
            print(f"      ASIN: {product.get('asin', 'N/A')}")
            print(f"      标题: {product.get('title', 'N/A')[:60]}...")
            print(f"      价格: ${product.get('price', 'N/A')}")
            print(f"      评分: {product.get('rating', 'N/A')}")
            print(f"      评论数: {product.get('review_count', 'N/A')}")
            print(f"      Prime信息: {product.get('prime_info', 'N/A')}")
            print(f"      是否广告: {'是' if product.get('is_sponsored', 0) else '否'}")
            print(f"      商品链接: {product.get('product_url', 'N/A')[:80]}...")
            print()
    
    return products

def test_save_to_page_tasks(products):
    """测试保存商品到详情页任务队列"""
    
    print("💾 测试保存到详情页任务队列")
    print("=" * 50)
    
    if not products:
        print("❌ 没有商品数据可测试")
        return
    
    # 使用测试类目ID
    test_category_id = 12345
    
    print(f"📋 准备保存 {len(products)} 个商品到详情页任务队列...")
    print(f"   类目ID: {test_category_id}")
    
    # 保存到详情页任务队列
    results = save_products_to_page_tasks(products, test_category_id)
    
    print(f"\n📊 保存结果:")
    print(f"   总计: {results['total']}")
    print(f"   新增: {results['inserted']}")
    print(f"   跳过: {results['skipped']}")
    print(f"   失败: {results['failed']}")
    
    if results['inserted'] > 0:
        print(f"   ✅ 成功创建 {results['inserted']} 个详情页抓取任务")
    
    if results['skipped'] > 0:
        print(f"   ⚠️ 跳过 {results['skipped']} 个重复的ASIN")
    
    if results['failed'] > 0:
        print(f"   ❌ 失败 {results['failed']} 个商品")
    
    return results

def test_duplicate_handling():
    """测试重复ASIN的处理"""
    
    print("\n🔄 测试重复ASIN处理")
    print("=" * 50)
    
    # 创建测试商品数据（包含重复ASIN）
    test_products = [
        {
            'asin': 'TEST_ASIN_001',
            'title': '测试商品1',
            'price': 19.99,
            'rating': 4.5,
            'review_count': 100,
            'image_url': 'https://example.com/image1.jpg',
            'product_url': 'https://www.amazon.com/dp/TEST_ASIN_001',
            'prime_info': 'Prime',
            'is_sponsored': 0
        },
        {
            'asin': 'TEST_ASIN_002',
            'title': '测试商品2',
            'price': 29.99,
            'rating': 4.8,
            'review_count': 200,
            'image_url': 'https://example.com/image2.jpg',
            'product_url': 'https://www.amazon.com/dp/TEST_ASIN_002',
            'prime_info': None,
            'is_sponsored': 1
        },
        {
            'asin': 'TEST_ASIN_001',  # 重复的ASIN
            'title': '测试商品1（重复）',
            'price': 21.99,
            'rating': 4.6,
            'review_count': 150,
            'image_url': 'https://example.com/image1_new.jpg',
            'product_url': 'https://www.amazon.com/dp/TEST_ASIN_001',
            'prime_info': 'Prime',
            'is_sponsored': 0
        }
    ]
    
    print(f"📋 测试数据包含 {len(test_products)} 个商品（其中1个重复ASIN）")
    
    # 第一次保存
    print("\n🔄 第一次保存...")
    results1 = save_products_to_page_tasks(test_products, 12345)
    print(f"   结果: 新增 {results1['inserted']}, 跳过 {results1['skipped']}")
    
    # 第二次保存相同数据
    print("\n🔄 第二次保存相同数据...")
    results2 = save_products_to_page_tasks(test_products, 12345)
    print(f"   结果: 新增 {results2['inserted']}, 跳过 {results2['skipped']}")
    
    if results2['skipped'] == len(set(p['asin'] for p in test_products)):
        print("   ✅ 重复检测正常工作")
    else:
        print("   ❌ 重复检测可能有问题")

def test_integration_workflow():
    """测试完整的集成工作流程"""
    
    print("\n🔄 测试完整集成工作流程")
    print("=" * 50)
    
    # 1. 创建列表页抓取任务
    test_url = "https://www.amazon.com/s?k=Kids+Party+Centerpieces&i=toys-and-games"
    test_category_id = 12345
    max_pages = 2
    
    print(f"📋 步骤1: 创建列表页抓取任务")
    print(f"   URL: {test_url}")
    print(f"   类目ID: {test_category_id}")
    print(f"   最大页数: {max_pages}")
    
    task_id = create_list_task(test_url, test_category_id, max_pages)
    
    if task_id:
        print(f"   ✅ 任务创建成功，任务ID: {task_id}")
        
        # 2. 查看任务状态
        print(f"\n📊 步骤2: 查看任务状态")
        tasks = get_task_status(task_id=task_id)
        for task in tasks:
            print(f"   任务ID: {task['id']}")
            print(f"   状态: {task['status']}")
            print(f"   进度: {task['crawled_pages']}/{task['max_pages_to_crawl']}")
        
        print(f"\n💡 提示: 要执行实际的分页抓取，请运行:")
        print(f"   from amazon_list_crawler_with_pagination import crawl_list_pages_with_pagination")
        print(f"   success = crawl_list_pages_with_pagination({task_id})")
        
    else:
        print(f"   ❌ 任务创建失败")

def main():
    """主测试函数"""
    
    print("🚀 Amazon分页爬虫与详情页任务队列集成测试")
    print("=" * 60)
    
    try:
        # 1. 测试增强的商品数据提取
        products = test_product_extraction_with_enhanced_data()
        
        # 2. 测试保存到详情页任务队列
        if products:
            test_save_to_page_tasks(products)
        
        # 3. 测试重复ASIN处理
        test_duplicate_handling()
        
        # 4. 测试完整集成工作流程
        test_integration_workflow()
        
        print("\n✅ 所有集成测试完成!")
        
        print("\n📋 下一步操作建议:")
        print("1. 确保数据库 xace200_lsh 中存在 zz_amazon_list_tasks 和 zz_amazon_page_tasks 表")
        print("2. 运行实际的分页抓取任务")
        print("3. 检查 zz_amazon_page_tasks 表中的详情页任务")
        print("4. 运行详情页爬虫处理这些任务")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
